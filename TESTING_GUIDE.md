# Image Enhancement Testing Guide

This guide shows you how to test your AI image enhancement system with custom images and webcam capture.

## 🚀 Quick Start

### 1. Test with a Custom Image
```bash
# Basic enhancement
python enhance_image.py your_photo.jpg

# With comparison
python enhance_image.py your_photo.jpg --compare

# Different model and scale
python enhance_image.py your_photo.jpg --model tiny --scale 4
```

### 2. Real-time Webcam Enhancement
```bash
# Basic webcam enhancement
python webcam_enhance.py

# High performance mode
python webcam_enhance.py --model tiny --fps 60

# High quality mode
python webcam_enhance.py --model efficient --scale 4

# Save enhanced frames
python webcam_enhance.py --save-frames
```

### 3. Comprehensive Testing
```bash
# Test single image
python test_enhancement.py image your_photo.jpg

# Test batch of images
python test_enhancement.py batch your_image_folder/

# Webcam testing
python test_enhancement.py webcam --model mobilenetv2

# Performance benchmark
python test_enhancement.py benchmark
```

## 📁 File Overview

### Core Scripts
- **`enhance_image.py`** - Single image enhancement (already exists)
- **`webcam_enhance.py`** - Real-time webcam enhancement (NEW)
- **`test_enhancement.py`** - Comprehensive testing tool (NEW)
- **`create_test_images.py`** - Generate test images (NEW)

### Features

#### `webcam_enhance.py` Features:
- ✅ Real-time webcam processing
- ✅ Multi-threaded for better performance
- ✅ FPS and processing time display
- ✅ Frame saving capability
- ✅ Interactive controls (q=quit, s=save, i=toggle info)
- ✅ Automatic performance optimization

#### `test_enhancement.py` Features:
- ✅ Single image testing
- ✅ Batch processing
- ✅ Webcam integration
- ✅ Performance benchmarking
- ✅ Automatic comparison generation

#### `create_test_images.py` Features:
- ✅ Synthetic test images
- ✅ Text images for OCR testing
- ✅ Pattern images
- ✅ Noisy and blurred images
- ✅ Various complexity levels

## 🎯 Testing Scenarios

### Scenario 1: Test Your Own Photos
```bash
# Create test images first (optional)
python create_test_images.py --count 5

# Test single photo
python test_enhancement.py image my_photo.jpg --model mobilenetv2 --scale 2

# Results will be in test_results/ folder
```

### Scenario 2: Batch Process Multiple Images
```bash
# Put your images in a folder, then:
python test_enhancement.py batch my_photos/ --model efficient --scale 4

# Results will be in batch_results/ folder
```

### Scenario 3: Real-time Webcam Testing
```bash
# Start webcam enhancement
python webcam_enhance.py --model tiny --fps 30

# Controls:
# - Press 'q' to quit
# - Press 's' to save current frame
# - Press 'i' to toggle info overlay
```

### Scenario 4: Performance Comparison
```bash
# Benchmark all models
python test_enhancement.py benchmark --iterations 20

# Results saved to benchmark_results.json
```

## 📊 Understanding Results

### Performance Metrics
- **FPS**: Frames per second (higher = better for real-time)
- **Processing Time**: Time to enhance one image (lower = better)
- **SSIM**: Structural similarity (higher = better quality)
- **Output Size**: Enhanced image dimensions

### Model Comparison
- **tiny**: Fastest, good for real-time (30-60 FPS)
- **mobilenetv2**: Balanced speed and quality (15-30 FPS)
- **efficient**: Best quality, slower (5-15 FPS)

### Scale Factors
- **2x**: Doubles resolution (640x480 → 1280x960)
- **4x**: Quadruples resolution (640x480 → 2560x1920)

## 🔧 Troubleshooting

### Common Issues

1. **"Could not open webcam"**
   - Check if camera is connected
   - Close other applications using camera
   - Try different camera index: `cv2.VideoCapture(1)`

2. **Low FPS in webcam mode**
   - Use `--model tiny` for better performance
   - Reduce `--scale` to 2
   - Lower target `--fps`

3. **Out of memory errors**
   - Use smaller images
   - Use `tiny` model
   - Reduce batch size

4. **Model loading errors**
   - Ensure models are trained and saved
   - Check model paths in config
   - Run training first if needed

### Performance Tips

1. **For Real-time (30+ FPS)**:
   ```bash
   python webcam_enhance.py --model tiny --scale 2 --fps 30
   ```

2. **For Best Quality**:
   ```bash
   python test_enhancement.py image photo.jpg --model efficient --scale 4
   ```

3. **For Batch Processing**:
   ```bash
   python test_enhancement.py batch folder/ --model mobilenetv2 --scale 2
   ```

## 📈 Next Steps

1. **Test with your own images** to see real-world performance
2. **Try different models** to find the best speed/quality balance
3. **Use webcam mode** to test real-time capabilities
4. **Run benchmarks** to measure performance improvements
5. **Save enhanced frames** for quality analysis

## 🎥 Webcam Controls

When running webcam enhancement:
- **'q'**: Quit application
- **'s'**: Save current frame pair (original + enhanced)
- **'i'**: Toggle information overlay on/off

Saved frames go to `webcam_captures/` folder with timestamps.

## 📝 Output Files

- **Enhanced images**: `*_enhanced_*.jpg`
- **Comparisons**: `*_comparison_*.jpg`
- **Benchmark results**: `benchmark_results.json`
- **Webcam captures**: `webcam_captures/original_*.jpg` and `enhanced_*.jpg`

Happy testing! 🚀
