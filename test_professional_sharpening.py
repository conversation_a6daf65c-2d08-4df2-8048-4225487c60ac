#!/usr/bin/env python3
"""
Test Professional Image Sharpening

Test the professional sharpening tool with various images and settings
to validate RGB color balance preservation, natural exposure, and edge detail enhancement.

Usage:
    python test_professional_sharpening.py --create-test-images
    python test_professional_sharpening.py --test-single photo.jpg
    python test_professional_sharpening.py --test-batch test_images/
    python test_professional_sharpening.py --benchmark
"""

import sys
import argparse
from pathlib import Path
import cv2
import numpy as np
import time
import json
from professional_sharpen import ProfessionalSharpener

def create_test_images():
    """Create test images for professional sharpening validation."""
    print("🎨 Creating test images for professional sharpening...")
    
    test_dir = Path("professional_test_images")
    test_dir.mkdir(exist_ok=True)
    
    # Test image 1: High contrast with fine details
    print("   Creating high contrast test image...")
    img1 = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Add fine grid pattern
    for i in range(0, 480, 10):
        cv2.line(img1, (0, i), (640, i), (100, 100, 100), 1)
    for j in range(0, 640, 10):
        cv2.line(img1, (j, 0), (j, 480), (100, 100, 100), 1)
    
    # Add colored shapes
    cv2.circle(img1, (160, 120), 50, (255, 100, 100), -1)
    cv2.circle(img1, (480, 120), 50, (100, 255, 100), -1)
    cv2.circle(img1, (320, 360), 50, (100, 100, 255), -1)
    
    cv2.imwrite(str(test_dir / "high_contrast_details.jpg"), img1)
    
    # Test image 2: Overexposure prone image
    print("   Creating overexposure test image...")
    img2 = np.ones((480, 640, 3), dtype=np.uint8) * 200  # Bright background
    
    # Add bright highlights
    cv2.rectangle(img2, (100, 100), (200, 200), (255, 255, 255), -1)
    cv2.rectangle(img2, (400, 100), (500, 200), (250, 250, 250), -1)
    
    # Add some darker details
    cv2.rectangle(img2, (50, 300), (150, 400), (50, 50, 50), -1)
    cv2.rectangle(img2, (450, 300), (550, 400), (80, 80, 80), -1)
    
    cv2.imwrite(str(test_dir / "overexposure_test.jpg"), img2)
    
    # Test image 3: Color balance test
    print("   Creating color balance test image...")
    img3 = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Create color gradients
    for i in range(480):
        for j in range(640):
            img3[i, j, 0] = int(255 * j / 640)  # Blue gradient
            img3[i, j, 1] = int(255 * i / 480)  # Green gradient
            img3[i, j, 2] = int(255 * (i + j) / (480 + 640))  # Red gradient
    
    # Add fine details
    for i in range(0, 480, 20):
        cv2.line(img3, (0, i), (640, i), (255, 255, 255), 1)
    
    cv2.imwrite(str(test_dir / "color_balance_test.jpg"), img3)
    
    # Test image 4: Edge detail test
    print("   Creating edge detail test image...")
    img4 = np.ones((480, 640, 3), dtype=np.uint8) * 128  # Gray background
    
    # Add various edge types
    # Sharp edges
    cv2.rectangle(img4, (50, 50), (150, 150), (0, 0, 0), 2)
    cv2.rectangle(img4, (200, 50), (300, 150), (255, 255, 255), 2)
    
    # Curved edges
    cv2.circle(img4, (400, 100), 50, (0, 0, 0), 2)
    cv2.ellipse(img4, (500, 100), (40, 60), 0, 0, 360, (255, 255, 255), 2)
    
    # Fine text-like details
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img4, "Fine Detail Test", (50, 300), font, 1, (0, 0, 0), 2)
    cv2.putText(img4, "Edge Enhancement", (50, 350), font, 0.8, (255, 255, 255), 1)
    
    cv2.imwrite(str(test_dir / "edge_detail_test.jpg"), img4)
    
    print(f"✅ Test images created in {test_dir}")
    return test_dir

def test_single_image(image_path, output_dir="professional_test_results"):
    """Test professional sharpening on a single image."""
    print(f"🔍 Testing professional sharpening on: {image_path}")
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    sharpener = ProfessionalSharpener()
    
    # Test different methods and strengths
    test_configs = [
        ('adaptive', 1.0),
        ('adaptive', 1.5),
        ('unsharp_mask', 1.0),
        ('laplacian', 0.8),
    ]
    
    results = []
    
    for method, strength in test_configs:
        print(f"\n   Testing {method} with strength {strength}...")
        
        output_file = output_path / f"{Path(image_path).stem}_{method}_{strength}.jpg"
        
        start_time = time.time()
        success = sharpener.sharpen_image(
            input_path=image_path,
            output_path=str(output_file),
            method=method,
            strength=strength,
            target_1080p=True,
            low_bandwidth=False
        )
        processing_time = time.time() - start_time
        
        results.append({
            'method': method,
            'strength': strength,
            'success': success,
            'processing_time': processing_time,
            'output_file': str(output_file)
        })
        
        if success:
            print(f"      ✅ Success - {processing_time*1000:.1f}ms")
        else:
            print(f"      ❌ Failed")
    
    return results

def test_batch_images(input_dir, output_dir="professional_batch_results"):
    """Test professional sharpening on multiple images."""
    print(f"📁 Testing batch professional sharpening: {input_dir}")
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Find all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(ext))
        image_files.extend(input_path.glob(ext.upper()))
    
    if not image_files:
        print(f"❌ No image files found in {input_dir}")
        return []
    
    print(f"📊 Found {len(image_files)} images")
    
    sharpener = ProfessionalSharpener()
    results = []
    total_time = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] Processing {image_file.name}...")
        
        output_file = output_path / f"{image_file.stem}_sharpened_adaptive_1080p.jpg"
        
        start_time = time.time()
        success = sharpener.sharpen_image(
            input_path=str(image_file),
            output_path=str(output_file),
            method='adaptive',
            strength=1.0,
            target_1080p=True,
            low_bandwidth=False
        )
        processing_time = time.time() - start_time
        total_time += processing_time
        
        results.append({
            'file': image_file.name,
            'success': success,
            'processing_time': processing_time,
            'quality_metrics': sharpener.quality_metrics if success else None
        })
    
    # Print summary
    successful = sum(1 for r in results if r['success'])
    print(f"\n📊 Batch Processing Summary:")
    print(f"   Total images: {len(image_files)}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {len(image_files) - successful}")
    print(f"   Total time: {total_time:.1f}s")
    print(f"   Average time per image: {total_time/len(image_files):.1f}s")
    
    return results

def benchmark_professional_sharpening():
    """Benchmark professional sharpening performance."""
    print("🏁 Benchmarking professional sharpening...")
    
    # Create test image
    test_image_path = "benchmark_test.jpg"
    test_image = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
    cv2.imwrite(test_image_path, test_image)
    
    sharpener = ProfessionalSharpener()
    
    methods = ['adaptive', 'unsharp_mask', 'laplacian']
    strengths = [0.8, 1.0, 1.5]
    iterations = 5
    
    results = {}
    
    for method in methods:
        for strength in strengths:
            config_name = f"{method}_{strength}"
            print(f"\n🧪 Testing {config_name}...")
            
            times = []
            for i in range(iterations):
                start_time = time.time()
                success = sharpener.sharpen_image(
                    input_path=test_image_path,
                    output_path=f"benchmark_output_{config_name}_{i}.jpg",
                    method=method,
                    strength=strength,
                    target_1080p=True,
                    low_bandwidth=False
                )
                end_time = time.time()
                
                if success:
                    times.append((end_time - start_time) * 1000)  # Convert to ms
            
            if times:
                avg_time = np.mean(times)
                std_time = np.std(times)
                min_time = np.min(times)
                max_time = np.max(times)
                
                results[config_name] = {
                    'method': method,
                    'strength': strength,
                    'avg_time_ms': avg_time,
                    'std_time_ms': std_time,
                    'min_time_ms': min_time,
                    'max_time_ms': max_time,
                    'fps_equivalent': 1000 / avg_time if avg_time > 0 else 0
                }
                
                print(f"   Average: {avg_time:.1f}ms ({1000/avg_time:.1f} FPS)")
                print(f"   Range: {min_time:.1f}-{max_time:.1f}ms")
    
    # Print summary table
    print(f"\n📊 Professional Sharpening Benchmark Results:")
    print("=" * 80)
    print(f"{'Method':<15} {'Strength':<8} {'Avg Time':<12} {'FPS':<8} {'Quality':<10}")
    print("-" * 80)
    
    for config_name, stats in results.items():
        quality_rating = "High" if stats['method'] == 'adaptive' else "Medium"
        print(f"{stats['method']:<15} {stats['strength']:<8} {stats['avg_time_ms']:<12.1f} "
              f"{stats['fps_equivalent']:<8.1f} {quality_rating:<10}")
    
    # Save results
    with open('professional_sharpening_benchmark.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to professional_sharpening_benchmark.json")
    
    # Cleanup
    Path(test_image_path).unlink(missing_ok=True)
    for config_name in results.keys():
        for i in range(iterations):
            Path(f"benchmark_output_{config_name}_{i}.jpg").unlink(missing_ok=True)
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Test Professional Image Sharpening")
    parser.add_argument("--create-test-images", action="store_true",
                       help="Create test images for validation")
    parser.add_argument("--test-single", help="Test single image")
    parser.add_argument("--test-batch", help="Test batch of images in directory")
    parser.add_argument("--benchmark", action="store_true",
                       help="Run performance benchmark")
    parser.add_argument("--all", action="store_true",
                       help="Run all tests")
    
    args = parser.parse_args()
    
    if not any([args.create_test_images, args.test_single, args.test_batch, 
                args.benchmark, args.all]):
        parser.print_help()
        return
    
    print("🎯 Professional Image Sharpening Test Suite")
    print("=" * 60)
    print("Testing RGB color balance preservation, natural exposure,")
    print("and fine edge detail enhancement capabilities.")
    print("=" * 60)
    
    if args.create_test_images or args.all:
        test_dir = create_test_images()
        if args.all:
            args.test_batch = str(test_dir)
    
    if args.test_single:
        test_single_image(args.test_single)
    
    if args.test_batch:
        test_batch_images(args.test_batch)
    
    if args.benchmark or args.all:
        benchmark_professional_sharpening()
    
    print("\n🎉 Professional sharpening tests completed!")
    print("Check the output directories for results.")

if __name__ == "__main__":
    main()
