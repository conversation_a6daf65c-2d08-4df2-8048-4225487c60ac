# HD Enhancement System Requirements
# True 1080p HD quality enhancement for images and webcam

# Core computer vision
opencv-python>=4.8.0
opencv-contrib-python>=4.8.0

# Numerical computing
numpy>=1.24.0
scipy>=1.10.0

# Image processing
Pillow>=10.0.0
scikit-image>=0.21.0

# AI/ML (optional but recommended for best quality)
torch>=2.0.0
torchvision>=0.15.0
onnx>=1.14.0
onnxruntime>=1.15.0

# Performance optimization
numba>=0.57.0

# Utilities
pathlib2>=2.3.7
argparse>=1.4.0
