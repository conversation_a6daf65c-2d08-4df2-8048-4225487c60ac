#!/usr/bin/env python3
"""
Ultimate HD Image Enhancer

Works with both manual image input and webcam.
Provides superior HD quality enhancement without GUI dependencies.
"""

import cv2
import numpy as np
import time
import argparse
from pathlib import Path
import json

class UltimateHDEnhancer:
    """Ultimate HD enhancer with superior quality algorithms."""
    
    def __init__(self):
        print("🎯 Ultimate HD Enhancer - Superior Quality")
        self.enhancement_stats = []
    
    def advanced_hd_enhance(self, image):
        """Advanced HD enhancement with superior quality."""
        start_time = time.time()
        
        # Get original dimensions
        orig_height, orig_width = image.shape[:2]
        
        # Step 1: Advanced noise reduction with edge preservation
        # Use Non-local Means Denoising for superior quality
        denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        
        # Step 2: Multi-scale unsharp masking for professional sharpening
        # Create multiple gaussian blurs for different detail levels
        blur1 = cv2.GaussianBlur(denoised, (0, 0), 1.0)
        blur2 = cv2.GaussianBlur(denoised, (0, 0), 2.0)
        blur3 = cv2.GaussianBlur(denoised, (0, 0), 4.0)
        
        # Apply multi-scale unsharp masking
        sharp1 = cv2.addWeighted(denoised, 1.3, blur1, -0.3, 0)
        sharp2 = cv2.addWeighted(sharp1, 1.2, blur2, -0.2, 0)
        enhanced = cv2.addWeighted(sharp2, 1.1, blur3, -0.1, 0)
        
        # Step 3: Advanced contrast enhancement using multiple methods
        # Convert to LAB color space for better processing
        lab = cv2.cvtColor(enhanced, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        l_enhanced = clahe.apply(l)
        
        # Merge back to BGR
        enhanced_lab = cv2.merge([l_enhanced, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        # Step 4: Edge enhancement for crisp details
        # Create edge mask
        gray = cv2.cvtColor(enhanced, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        # Enhance edges
        enhanced = cv2.addWeighted(enhanced, 0.9, edges_colored, 0.1, 0)
        
        # Step 5: Color enhancement
        # Convert to HSV for color adjustments
        hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV)
        h, s, v = cv2.split(hsv)
        
        # Enhance saturation slightly
        s = cv2.multiply(s, 1.1)
        s = np.clip(s, 0, 255).astype(np.uint8)
        
        # Merge back
        enhanced_hsv = cv2.merge([h, s, v])
        enhanced = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
        
        # Step 6: Scale to HD resolution with superior interpolation
        target_height = max(1080, orig_height)  # Ensure at least 1080p
        if orig_height < target_height:
            scale = target_height / orig_height
            new_width = int(orig_width * scale)
            
            # Use INTER_LANCZOS4 for best quality upscaling
            enhanced = cv2.resize(enhanced, (new_width, target_height), 
                                interpolation=cv2.INTER_LANCZOS4)
        
        # Step 7: Final quality refinement
        # Apply subtle bilateral filter to smooth any artifacts
        final = cv2.bilateralFilter(enhanced, 5, 50, 50)
        
        # Blend with enhanced version for natural result
        result = cv2.addWeighted(enhanced, 0.8, final, 0.2, 0)
        
        processing_time = (time.time() - start_time) * 1000
        
        # Calculate quality metrics
        final_height, final_width = result.shape[:2]
        scale_factor = final_width / orig_width
        
        stats = {
            'original_size': (orig_width, orig_height),
            'enhanced_size': (final_width, final_height),
            'scale_factor': scale_factor,
            'processing_time_ms': processing_time,
            'is_hd': final_height >= 1080,
            'quality_level': 'ULTRA_HD' if final_height >= 1080 else 'HD'
        }
        
        self.enhancement_stats.append(stats)
        
        return result, stats
    
    def create_quality_comparison(self, original, enhanced, stats):
        """Create detailed quality comparison image."""
        # Resize original to match enhanced height for comparison
        orig_h, orig_w = original.shape[:2]
        enh_h, enh_w = enhanced.shape[:2]
        
        # Scale original to same height
        scale = enh_h / orig_h
        new_orig_w = int(orig_w * scale)
        original_scaled = cv2.resize(original, (new_orig_w, enh_h), 
                                   interpolation=cv2.INTER_LANCZOS4)
        
        # Create side-by-side comparison
        comparison = np.hstack([original_scaled, enhanced])
        
        # Add detailed information overlay
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        thickness = 2
        
        # Background for text
        overlay = comparison.copy()
        cv2.rectangle(overlay, (10, 10), (comparison.shape[1]-10, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, comparison, 0.2, 0, comparison)
        
        # Add comprehensive information
        info_lines = [
            f"ORIGINAL: {stats['original_size'][0]}x{stats['original_size'][1]}",
            f"ENHANCED: {stats['enhanced_size'][0]}x{stats['enhanced_size'][1]}",
            f"SCALE: {stats['scale_factor']:.1f}x | QUALITY: {stats['quality_level']}",
            f"PROCESSING: {stats['processing_time_ms']:.1f}ms",
            f"HD STATUS: {'✓ TRUE HD' if stats['is_hd'] else '✗ NOT HD'}",
            f"ENHANCEMENT: ULTRA QUALITY ALGORITHM"
        ]
        
        y_start = 35
        for i, line in enumerate(info_lines):
            color = (0, 255, 0) if i < 2 else (255, 255, 255)
            if 'HD STATUS' in line and stats['is_hd']:
                color = (0, 255, 0)
            elif 'HD STATUS' in line:
                color = (0, 0, 255)
            
            cv2.putText(comparison, line, (20, y_start + i*25), 
                       font, font_scale, color, thickness)
        
        # Add separator line
        cv2.line(comparison, (original_scaled.shape[1], 0), 
                (original_scaled.shape[1], comparison.shape[0]), (255, 255, 255), 3)
        
        return comparison
    
    def enhance_single_image(self, input_path, output_dir=None):
        """Enhance a single image with superior quality."""
        input_path = Path(input_path)
        if not input_path.exists():
            print(f"❌ Image not found: {input_path}")
            return False
        
        print(f"🎯 Enhancing: {input_path.name}")
        
        # Load image
        original = cv2.imread(str(input_path))
        if original is None:
            print(f"❌ Cannot load image: {input_path}")
            return False
        
        # Enhance
        enhanced, stats = self.advanced_hd_enhance(original)
        
        # Create output directory
        if output_dir is None:
            output_dir = input_path.parent / "hd_enhanced"
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(exist_ok=True)
        
        # Save enhanced image
        enhanced_path = output_dir / f"{input_path.stem}_ultra_hd{input_path.suffix}"
        cv2.imwrite(str(enhanced_path), enhanced, [cv2.IMWRITE_JPEG_QUALITY, 100])
        
        # Create and save comparison
        comparison = self.create_quality_comparison(original, enhanced, stats)
        comparison_path = output_dir / f"{input_path.stem}_comparison{input_path.suffix}"
        cv2.imwrite(str(comparison_path), comparison, [cv2.IMWRITE_JPEG_QUALITY, 100])
        
        # Save stats
        stats_path = output_dir / f"{input_path.stem}_stats.json"
        with open(stats_path, 'w') as f:
            json.dump(stats, f, indent=2)
        
        # Print results
        print(f"✅ Enhancement completed:")
        print(f"   Original: {stats['original_size'][0]}x{stats['original_size'][1]}")
        print(f"   Enhanced: {stats['enhanced_size'][0]}x{stats['enhanced_size'][1]}")
        print(f"   Scale: {stats['scale_factor']:.1f}x")
        print(f"   Quality: {stats['quality_level']}")
        print(f"   Processing: {stats['processing_time_ms']:.1f}ms")
        print(f"   HD Status: {'✅ TRUE HD' if stats['is_hd'] else '❌ NOT HD'}")
        print(f"📁 Files saved:")
        print(f"   Enhanced: {enhanced_path}")
        print(f"   Comparison: {comparison_path}")
        print(f"   Stats: {stats_path}")
        
        return True
    
    def run_webcam_capture(self, duration=60, save_interval=3):
        """Capture and enhance webcam frames (no GUI needed)."""
        print(f"🎥 Starting webcam HD enhancement capture...")
        print(f"⏱️  Duration: {duration} seconds")
        print(f"💾 Save interval: {save_interval} seconds")
        print("Press Ctrl+C to stop early")
        
        # Create output directory
        output_dir = Path("webcam_hd_enhanced")
        output_dir.mkdir(exist_ok=True)
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam")
            return False
        
        # Set webcam properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        print(f"📹 Webcam: {actual_width}x{actual_height}")
        
        start_time = time.time()
        last_save_time = start_time
        frame_count = 0
        saved_count = 0
        
        try:
            while time.time() - start_time < duration:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Cannot read frame")
                    continue
                
                frame_count += 1
                current_time = time.time()
                
                # Save and enhance at intervals
                if current_time - last_save_time >= save_interval:
                    print(f"📸 Processing frame {frame_count}...")
                    
                    # Enhance frame
                    enhanced, stats = self.advanced_hd_enhance(frame)
                    
                    # Create comparison
                    comparison = self.create_quality_comparison(frame, enhanced, stats)
                    
                    # Save files
                    timestamp = int(current_time)
                    
                    # Save original
                    orig_path = output_dir / f"original_{timestamp}.jpg"
                    cv2.imwrite(str(orig_path), frame, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    # Save enhanced
                    enhanced_path = output_dir / f"ultra_hd_{timestamp}.jpg"
                    cv2.imwrite(str(enhanced_path), enhanced, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    # Save comparison
                    comp_path = output_dir / f"comparison_{timestamp}.jpg"
                    cv2.imwrite(str(comp_path), comparison, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    # Save stats
                    stats_path = output_dir / f"stats_{timestamp}.json"
                    with open(stats_path, 'w') as f:
                        json.dump(stats, f, indent=2)
                    
                    saved_count += 1
                    print(f"✅ Saved set {saved_count}:")
                    print(f"   Original: {stats['original_size'][0]}x{stats['original_size'][1]}")
                    print(f"   Enhanced: {stats['enhanced_size'][0]}x{stats['enhanced_size'][1]}")
                    print(f"   Quality: {stats['quality_level']}")
                    print(f"   Processing: {stats['processing_time_ms']:.1f}ms")
                    
                    last_save_time = current_time
                
                # Progress update
                elapsed = current_time - start_time
                remaining = duration - elapsed
                if frame_count % 60 == 0:
                    print(f"⏱️  {remaining:.1f}s remaining, {saved_count} HD sets saved")
        
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        
        finally:
            cap.release()
            
            print(f"\n📊 Webcam HD Enhancement Summary:")
            print(f"   Duration: {time.time() - start_time:.1f}s")
            print(f"   Frames processed: {frame_count}")
            print(f"   HD sets saved: {saved_count}")
            print(f"   Output directory: {output_dir}")
            
            if self.enhancement_stats:
                avg_time = np.mean([s['processing_time_ms'] for s in self.enhancement_stats])
                hd_count = sum(1 for s in self.enhancement_stats if s['is_hd'])
                print(f"   Average processing: {avg_time:.1f}ms")
                print(f"   HD quality achieved: {hd_count}/{len(self.enhancement_stats)}")
            
            print(f"\n🎯 Check {output_dir} for your Ultra HD enhanced images!")
        
        return True

def main():
    parser = argparse.ArgumentParser(
        description="Ultimate HD Image Enhancer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Single image enhancement
  python ultimate_hd_enhancer.py --image photo.jpg
  python ultimate_hd_enhancer.py --image photo.jpg --output ./results
  
  # Webcam capture and enhancement
  python ultimate_hd_enhancer.py --webcam
  python ultimate_hd_enhancer.py --webcam --duration 30 --interval 2
  
  # Batch processing
  python ultimate_hd_enhancer.py --batch ./photos
        """
    )
    
    parser.add_argument("--image", help="Single image to enhance")
    parser.add_argument("--output", help="Output directory")
    parser.add_argument("--webcam", action="store_true", help="Webcam capture mode")
    parser.add_argument("--duration", type=int, default=60, help="Webcam duration (seconds)")
    parser.add_argument("--interval", type=int, default=3, help="Save interval (seconds)")
    parser.add_argument("--batch", help="Batch process directory")
    
    args = parser.parse_args()
    
    if not any([args.image, args.webcam, args.batch]):
        parser.print_help()
        return
    
    print("🎯 Ultimate HD Image Enhancer")
    print("=" * 50)
    print("Superior quality HD enhancement")
    print("No GUI dependencies - saves to disk")
    print("=" * 50)
    
    enhancer = UltimateHDEnhancer()
    
    if args.image:
        enhancer.enhance_single_image(args.image, args.output)
    
    if args.webcam:
        enhancer.run_webcam_capture(args.duration, args.interval)
    
    if args.batch:
        batch_dir = Path(args.batch)
        if batch_dir.exists():
            image_files = list(batch_dir.glob("*.jpg")) + list(batch_dir.glob("*.png"))
            print(f"📁 Found {len(image_files)} images to process")
            
            for img_file in image_files:
                enhancer.enhance_single_image(img_file, args.output)
        else:
            print(f"❌ Batch directory not found: {batch_dir}")

if __name__ == "__main__":
    main()
