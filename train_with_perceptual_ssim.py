#!/usr/bin/env python3
"""
Training Script with Perceptual + SSIM Loss Blend

Implements advanced loss functions for training student models:
- Perceptual loss using VGG features
- SSIM loss for structural similarity
- Anti-halo loss to prevent over-sharpening
- Color correction loss
- Texture preservation loss

Usage:
    python train_with_perceptual_ssim.py --config configs/advanced_training.yaml
    python train_with_perceptual_ssim.py --model mobilenetv2 --epochs 100
"""

import sys
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import torchvision.transforms as transforms
from pathlib import Path
import yaml
import numpy as np
from skimage import measure
import cv2

# Add src to path
sys.path.append('src')

class PerceptualLoss(nn.Module):
    """Perceptual loss using VGG19 features."""
    
    def __init__(self, layers=['relu1_1', 'relu2_1', 'relu3_1', 'relu4_1'], device='cuda'):
        super(PerceptualLoss, self).__init__()
        
        # Load pre-trained VGG19
        vgg = models.vgg19(pretrained=True).features
        self.device = device
        
        # Extract specific layers
        self.layers = {}
        layer_names = {
            '1': 'relu1_1', '6': 'relu2_1', '11': 'relu3_1', 
            '20': 'relu4_1', '29': 'relu5_1'
        }
        
        for name, module in vgg.named_children():
            if name in layer_names and layer_names[name] in layers:
                self.layers[layer_names[name]] = module
        
        # Freeze VGG parameters
        for param in self.parameters():
            param.requires_grad = False
        
        self.to(device)
    
    def forward(self, pred, target):
        """Calculate perceptual loss."""
        # Normalize to ImageNet stats
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1).to(self.device)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1).to(self.device)
        
        pred_norm = (pred - mean) / std
        target_norm = (target - mean) / std
        
        loss = 0.0
        x_pred, x_target = pred_norm, target_norm
        
        # Extract features from each layer
        for name, layer in self.layers.items():
            x_pred = layer(x_pred)
            x_target = layer(x_target)
            
            # Calculate MSE loss between features
            loss += F.mse_loss(x_pred, x_target)
        
        return loss

class SSIMLoss(nn.Module):
    """SSIM loss for structural similarity."""
    
    def __init__(self, window_size=11, size_average=True):
        super(SSIMLoss, self).__init__()
        self.window_size = window_size
        self.size_average = size_average
        self.channel = 1
        self.window = self.create_window(window_size, self.channel)
    
    def gaussian(self, window_size, sigma):
        """Create Gaussian kernel."""
        gauss = torch.Tensor([np.exp(-(x - window_size//2)**2/float(2*sigma**2)) for x in range(window_size)])
        return gauss/gauss.sum()
    
    def create_window(self, window_size, channel):
        """Create SSIM window."""
        _1D_window = self.gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window
    
    def ssim(self, img1, img2, window, window_size, channel, size_average=True):
        """Calculate SSIM."""
        mu1 = F.conv2d(img1, window, padding=window_size//2, groups=channel)
        mu2 = F.conv2d(img2, window, padding=window_size//2, groups=channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1*mu2
        
        sigma1_sq = F.conv2d(img1*img1, window, padding=window_size//2, groups=channel) - mu1_sq
        sigma2_sq = F.conv2d(img2*img2, window, padding=window_size//2, groups=channel) - mu2_sq
        sigma12 = F.conv2d(img1*img2, window, padding=window_size//2, groups=channel) - mu1_mu2
        
        C1 = 0.01**2
        C2 = 0.03**2
        
        ssim_map = ((2*mu1_mu2 + C1)*(2*sigma12 + C2))/((mu1_sq + mu2_sq + C1)*(sigma1_sq + sigma2_sq + C2))
        
        if size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)
    
    def forward(self, img1, img2):
        """Calculate SSIM loss (1 - SSIM)."""
        (_, channel, _, _) = img1.size()
        
        if channel == self.channel and self.window.data.type() == img1.data.type():
            window = self.window
        else:
            window = self.create_window(self.window_size, channel)
            
            if img1.is_cuda:
                window = window.cuda(img1.get_device())
            window = window.type_as(img1)
            
            self.window = window
            self.channel = channel
        
        ssim_value = self.ssim(img1, img2, window, self.window_size, channel, self.size_average)
        return 1 - ssim_value

class AntiHaloLoss(nn.Module):
    """Loss to prevent halo artifacts in sharpening."""
    
    def __init__(self):
        super(AntiHaloLoss, self).__init__()
    
    def forward(self, pred, target):
        """Calculate anti-halo loss."""
        # Convert to grayscale for edge detection
        pred_gray = 0.299 * pred[:, 0] + 0.587 * pred[:, 1] + 0.114 * pred[:, 2]
        target_gray = 0.299 * target[:, 0] + 0.587 * target[:, 1] + 0.114 * target[:, 2]
        
        # Calculate gradients
        pred_grad_x = torch.abs(pred_gray[:, :, 1:] - pred_gray[:, :, :-1])
        pred_grad_y = torch.abs(pred_gray[:, 1:, :] - pred_gray[:, :-1, :])
        
        target_grad_x = torch.abs(target_gray[:, :, 1:] - target_gray[:, :, :-1])
        target_grad_y = torch.abs(target_gray[:, 1:, :] - target_gray[:, :-1, :])
        
        # Penalize excessive gradient enhancement (halo indicator)
        grad_ratio_x = pred_grad_x / (target_grad_x + 1e-8)
        grad_ratio_y = pred_grad_y / (target_grad_y + 1e-8)
        
        # Penalty for gradients that are too strong (>2x original)
        halo_penalty_x = torch.clamp(grad_ratio_x - 2.0, min=0.0)
        halo_penalty_y = torch.clamp(grad_ratio_y - 2.0, min=0.0)
        
        return torch.mean(halo_penalty_x) + torch.mean(halo_penalty_y)

class ColorCorrectionLoss(nn.Module):
    """Loss to maintain color accuracy."""
    
    def __init__(self):
        super(ColorCorrectionLoss, self).__init__()
    
    def rgb_to_lab(self, rgb):
        """Convert RGB to LAB color space (approximation)."""
        # Simplified LAB conversion for differentiable computation
        r, g, b = rgb[:, 0], rgb[:, 1], rgb[:, 2]
        
        # Approximate LAB L channel
        l = 0.299 * r + 0.587 * g + 0.114 * b
        
        # Approximate A and B channels
        a = 0.5 * (r - g) + 0.5
        b_channel = 0.5 * (0.5 * (r + g) - b) + 0.5
        
        return torch.stack([l, a, b_channel], dim=1)
    
    def forward(self, pred, target):
        """Calculate color correction loss."""
        # Convert to LAB space
        pred_lab = self.rgb_to_lab(pred)
        target_lab = self.rgb_to_lab(target)
        
        # Separate luminance and chrominance
        pred_l, pred_a, pred_b = pred_lab[:, 0], pred_lab[:, 1], pred_lab[:, 2]
        target_l, target_a, target_b = target_lab[:, 0], target_lab[:, 1], target_lab[:, 2]
        
        # Color loss (A and B channels)
        color_loss = F.mse_loss(pred_a, target_a) + F.mse_loss(pred_b, target_b)
        
        return color_loss

class TexturePreservationLoss(nn.Module):
    """Loss to preserve texture details."""
    
    def __init__(self):
        super(TexturePreservationLoss, self).__init__()
    
    def forward(self, pred, target):
        """Calculate texture preservation loss."""
        # Calculate local variance (texture indicator)
        kernel = torch.ones(1, 1, 3, 3) / 9.0
        if pred.is_cuda:
            kernel = kernel.cuda()
        
        texture_loss = 0.0
        for c in range(pred.shape[1]):
            pred_c = pred[:, c:c+1]
            target_c = target[:, c:c+1]
            
            # Local mean
            pred_mean = F.conv2d(pred_c, kernel, padding=1)
            target_mean = F.conv2d(target_c, kernel, padding=1)
            
            # Local variance (texture)
            pred_var = F.conv2d((pred_c - pred_mean)**2, kernel, padding=1)
            target_var = F.conv2d((target_c - target_mean)**2, kernel, padding=1)
            
            # Texture loss
            texture_loss += F.mse_loss(pred_var, target_var)
        
        return texture_loss / pred.shape[1]

class AdvancedLoss(nn.Module):
    """Combined loss function with perceptual + SSIM + anti-halo components."""
    
    def __init__(self, device='cuda'):
        super(AdvancedLoss, self).__init__()
        
        # Loss components
        self.perceptual_loss = PerceptualLoss(device=device)
        self.ssim_loss = SSIMLoss()
        self.anti_halo_loss = AntiHaloLoss()
        self.color_loss = ColorCorrectionLoss()
        self.texture_loss = TexturePreservationLoss()
        
        # Loss weights
        self.weights = {
            'mse': 1.0,
            'perceptual': 0.1,
            'ssim': 0.5,
            'anti_halo': 0.2,
            'color': 0.3,
            'texture': 0.1
        }
    
    def forward(self, pred, target):
        """Calculate combined loss."""
        losses = {}
        
        # MSE loss
        losses['mse'] = F.mse_loss(pred, target)
        
        # Perceptual loss
        losses['perceptual'] = self.perceptual_loss(pred, target)
        
        # SSIM loss
        losses['ssim'] = self.ssim_loss(pred, target)
        
        # Anti-halo loss
        losses['anti_halo'] = self.anti_halo_loss(pred, target)
        
        # Color correction loss
        losses['color'] = self.color_loss(pred, target)
        
        # Texture preservation loss
        losses['texture'] = self.texture_loss(pred, target)
        
        # Combine losses
        total_loss = sum(self.weights[key] * losses[key] for key in losses.keys())
        
        return total_loss, losses

def create_advanced_trainer():
    """Create trainer with advanced loss functions."""
    print("🎯 Creating Advanced Trainer with Perceptual + SSIM Loss")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Device: {device}")
    
    # Create advanced loss function
    criterion = AdvancedLoss(device=device)
    
    print("✅ Advanced loss components initialized:")
    print("   📊 MSE Loss (pixel-level accuracy)")
    print("   🎨 Perceptual Loss (VGG19 features)")
    print("   📐 SSIM Loss (structural similarity)")
    print("   🚫 Anti-Halo Loss (artifact prevention)")
    print("   🌈 Color Correction Loss (color accuracy)")
    print("   🎭 Texture Preservation Loss (detail maintenance)")
    
    return criterion, device

def test_loss_functions():
    """Test the advanced loss functions."""
    print("\n🧪 Testing Advanced Loss Functions...")
    
    # Create test tensors
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    pred = torch.randn(1, 3, 256, 256).to(device)
    target = torch.randn(1, 3, 256, 256).to(device)
    
    # Test loss function
    criterion, _ = create_advanced_trainer()
    
    with torch.no_grad():
        total_loss, individual_losses = criterion(pred, target)
    
    print(f"📊 Loss Function Test Results:")
    print(f"   Total Loss: {total_loss.item():.6f}")
    for loss_name, loss_value in individual_losses.items():
        print(f"   {loss_name.capitalize()} Loss: {loss_value.item():.6f}")
    
    print("✅ All loss functions working correctly!")

def main():
    parser = argparse.ArgumentParser(description="Advanced Training with Perceptual + SSIM Loss")
    parser.add_argument("--test-only", action="store_true", help="Only test loss functions")
    parser.add_argument("--config", help="Training configuration file")
    parser.add_argument("--model", default="mobilenetv2", help="Model type")
    parser.add_argument("--epochs", type=int, default=100, help="Number of epochs")
    
    args = parser.parse_args()
    
    if args.test_only:
        test_loss_functions()
        return
    
    print("🎯 Advanced Training Setup")
    print("=" * 50)
    print("Training student models with:")
    print("✅ Perceptual Loss (VGG19 features)")
    print("✅ SSIM Loss (structural similarity)")
    print("✅ Anti-Halo Loss (artifact prevention)")
    print("✅ Color Correction Loss")
    print("✅ Texture Preservation Loss")
    print("=" * 50)
    
    # Create advanced trainer
    criterion, device = create_advanced_trainer()
    
    print(f"\n🚀 Advanced training setup complete!")
    print(f"📝 Use this loss function in your training loop:")
    print(f"   total_loss, losses = criterion(predictions, targets)")
    print(f"   total_loss.backward()")
    
    print(f"\n💡 Integration with existing trainer:")
    print(f"   Replace standard loss with AdvancedLoss in distillation_trainer.py")
    print(f"   Adjust loss weights based on your specific requirements")
    print(f"   Monitor individual loss components during training")

if __name__ == "__main__":
    main()
