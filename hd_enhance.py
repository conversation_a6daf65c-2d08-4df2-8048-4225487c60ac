#!/usr/bin/env python3
"""
True HD Quality Image Enhancement

Provides genuine 1080p HD quality enhancement without artistic effects.
Focuses on sharpening, detail enhancement, and quality improvement.

Usage:
    python hd_enhance.py input_image.jpg
    python hd_enhance.py input_image.jpg --output hd_output.jpg
    python hd_enhance.py input_image.jpg --webcam
"""

import sys
import argparse
from pathlib import Path
import cv2
import numpy as np
import time
import threading
import queue

# Add src to path
sys.path.append('src')

class HDEnhancer:
    """True HD quality enhancer for images and webcam."""
    
    def __init__(self):
        self.model = None
        self.load_ai_model()
    
    def load_ai_model(self):
        """Load AI model for enhancement."""
        try:
            from src.models.student_models import StudentModelFactory
            import torch
            
            # Use efficient model for best quality
            self.model = StudentModelFactory.create_student('efficient', scale_factor=2)
            self.model.eval()
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model.to(self.device)
            print(f"✅ AI model loaded on {self.device}")
            
        except Exception as e:
            print(f"⚠️  AI model not available, using traditional enhancement: {e}")
            self.model = None
    
    def enhance_with_ai(self, image):
        """Enhance image using AI model."""
        if self.model is None:
            return image
        
        try:
            import torch
            
            # Preprocess
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
            tensor = tensor.to(self.device)
            
            # Enhance
            with torch.no_grad():
                enhanced_tensor = self.model(tensor)
            
            # Postprocess
            enhanced_np = enhanced_tensor.cpu().squeeze(0).permute(1, 2, 0).numpy()
            enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
            enhanced_bgr = cv2.cvtColor(enhanced_np, cv2.COLOR_RGB2BGR)
            
            return enhanced_bgr
            
        except Exception as e:
            print(f"⚠️  AI enhancement failed: {e}")
            return image
    
    def traditional_enhance(self, image):
        """High-quality traditional enhancement."""
        # Convert to float for processing
        img_float = image.astype(np.float32) / 255.0
        
        # 1. Noise reduction while preserving edges
        denoised = cv2.bilateralFilter(image, 9, 75, 75)
        
        # 2. Unsharp masking for sharpening
        gaussian = cv2.GaussianBlur(denoised, (0, 0), 2.0)
        unsharp = cv2.addWeighted(denoised, 1.5, gaussian, -0.5, 0)
        
        # 3. Contrast enhancement
        lab = cv2.cvtColor(unsharp, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # 4. Final sharpening
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # Blend for natural result
        result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
        
        return result
    
    def resize_to_hd(self, image, target_height=1080):
        """Resize image to HD quality maintaining aspect ratio."""
        current_height, current_width = image.shape[:2]
        
        if current_height >= target_height:
            return image
        
        # Calculate new dimensions
        scale = target_height / current_height
        new_width = int(current_width * scale)
        
        # Use high-quality interpolation
        resized = cv2.resize(image, (new_width, target_height), interpolation=cv2.INTER_LANCZOS4)
        
        return resized
    
    def enhance_image(self, image):
        """Main enhancement pipeline."""
        start_time = time.time()
        
        # Step 1: AI enhancement if available
        if self.model is not None:
            enhanced = self.enhance_with_ai(image)
        else:
            enhanced = self.traditional_enhance(image)
        
        # Step 2: Resize to HD
        hd_enhanced = self.resize_to_hd(enhanced, target_height=1080)
        
        processing_time = time.time() - start_time
        
        return hd_enhanced, processing_time
    
    def process_single_image(self, input_path, output_path=None):
        """Process a single image file."""
        print(f"🎯 HD Enhancement: {input_path}")
        
        # Load image
        image = cv2.imread(input_path)
        if image is None:
            print(f"❌ Error: Could not load image {input_path}")
            return False
        
        original_shape = image.shape
        print(f"📏 Original size: {original_shape[1]}x{original_shape[0]}")
        
        # Enhance
        enhanced, processing_time = self.enhance_image(image)
        
        final_shape = enhanced.shape
        print(f"📐 Enhanced size: {final_shape[1]}x{final_shape[0]}")
        print(f"⏱️  Processing time: {processing_time*1000:.1f}ms")
        
        # Save result
        if output_path is None:
            input_file = Path(input_path)
            output_path = input_file.parent / f"{input_file.stem}_hd_enhanced{input_file.suffix}"
        
        # Save with maximum quality
        if str(output_path).lower().endswith(('.jpg', '.jpeg')):
            cv2.imwrite(str(output_path), enhanced, [cv2.IMWRITE_JPEG_QUALITY, 100])
        else:
            cv2.imwrite(str(output_path), enhanced)
        
        print(f"✅ HD enhanced image saved: {output_path}")
        
        # Calculate improvement
        scale_achieved = final_shape[1] / original_shape[1]
        print(f"📈 Scale improvement: {scale_achieved:.1f}x")
        
        return True

class HDWebcamEnhancer:
    """Real-time HD webcam enhancer."""
    
    def __init__(self):
        self.enhancer = HDEnhancer()
        self.frame_queue = queue.Queue(maxsize=2)
        self.result_queue = queue.Queue(maxsize=2)
        self.processing_thread = None
        self.running = False
        
        # Performance tracking
        self.fps_history = []
        self.processing_times = []
    
    def processing_worker(self):
        """Worker thread for frame processing."""
        while self.running:
            try:
                frame = self.frame_queue.get(timeout=0.1)
                
                # Enhance frame
                enhanced, proc_time = self.enhancer.enhance_image(frame)
                self.processing_times.append(proc_time * 1000)
                
                # Put result
                if not self.result_queue.full():
                    self.result_queue.put(enhanced)
                
                self.frame_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Processing error: {e}")
    
    def add_performance_overlay(self, frame, fps, processing_time):
        """Add performance information overlay."""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (0, 255, 0)
        thickness = 2
        
        # Background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (350, 100), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Text
        texts = [
            f"HD Enhancement: ON",
            f"FPS: {fps:.1f}",
            f"Processing: {processing_time:.1f}ms",
            f"Resolution: {frame.shape[1]}x{frame.shape[0]}"
        ]
        
        y_offset = 30
        for text in texts:
            cv2.putText(frame, text, (15, y_offset), font, font_scale, color, thickness)
            y_offset += 20
    
    def run_webcam(self):
        """Run HD webcam enhancement."""
        print("🎥 Starting HD Webcam Enhancement...")
        print("Controls: 'q' to quit, 's' to save frame, 'i' to toggle info")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Error: Could not open webcam")
            return
        
        # Set webcam to highest quality
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Get actual resolution
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        print(f"📹 Webcam resolution: {actual_width}x{actual_height}")
        
        # Start processing thread
        self.running = True
        self.processing_thread = threading.Thread(target=self.processing_worker)
        self.processing_thread.start()
        
        show_info = True
        enhanced_frame = None
        last_frame_time = time.time()
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Error reading from webcam")
                    break
                
                current_time = time.time()
                
                # Add frame to processing queue
                if not self.frame_queue.full():
                    self.frame_queue.put(frame.copy())
                
                # Get enhanced frame if available
                try:
                    enhanced_frame = self.result_queue.get_nowait()
                except queue.Empty:
                    pass
                
                # Calculate FPS
                fps = 1.0 / (current_time - last_frame_time)
                self.fps_history.append(fps)
                if len(self.fps_history) > 30:
                    self.fps_history.pop(0)
                
                last_frame_time = current_time
                
                # Display frame
                display_frame = enhanced_frame if enhanced_frame is not None else frame
                
                if show_info:
                    avg_fps = sum(self.fps_history) / len(self.fps_history)
                    avg_proc_time = sum(self.processing_times[-10:]) / len(self.processing_times[-10:]) if self.processing_times else 0
                    self.add_performance_overlay(display_frame, avg_fps, avg_proc_time)
                
                cv2.imshow('HD Enhanced Webcam', display_frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s') and enhanced_frame is not None:
                    timestamp = int(time.time())
                    filename = f"hd_webcam_capture_{timestamp}.jpg"
                    cv2.imwrite(filename, enhanced_frame, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    print(f"💾 HD frame saved: {filename}")
                    frame_count += 1
                elif key == ord('i'):
                    show_info = not show_info
                    print(f"ℹ️  Info overlay: {'ON' if show_info else 'OFF'}")
        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
        
        finally:
            # Cleanup
            self.running = False
            if self.processing_thread:
                self.processing_thread.join()
            
            cap.release()
            cv2.destroyAllWindows()
            
            # Print statistics
            if self.fps_history:
                avg_fps = sum(self.fps_history) / len(self.fps_history)
                print(f"📊 Average FPS: {avg_fps:.1f}")
            
            if self.processing_times:
                avg_proc_time = sum(self.processing_times) / len(self.processing_times)
                print(f"⏱️  Average processing time: {avg_proc_time:.1f}ms")
            
            print(f"💾 Saved {frame_count} HD frames")

def main():
    parser = argparse.ArgumentParser(
        description="True HD Quality Image Enhancement",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python hd_enhance.py photo.jpg                    # Enhance single image
  python hd_enhance.py photo.jpg --output hd.jpg    # Custom output
  python hd_enhance.py --webcam                      # HD webcam enhancement
  python hd_enhance.py photo.jpg --webcam           # Process image then webcam
        """
    )
    
    parser.add_argument("input", nargs='?', help="Input image path (optional)")
    parser.add_argument("--output", "-o", help="Output image path")
    parser.add_argument("--webcam", "-w", action="store_true", help="Start HD webcam enhancement")
    
    args = parser.parse_args()
    
    if not args.input and not args.webcam:
        parser.print_help()
        return
    
    print("🎯 True HD Quality Enhancement")
    print("=" * 50)
    print("Genuine 1080p HD quality without artistic effects")
    print("=" * 50)
    
    # Process single image if provided
    if args.input:
        if not Path(args.input).exists():
            print(f"❌ Error: Input file '{args.input}' not found")
            return
        
        enhancer = HDEnhancer()
        success = enhancer.process_single_image(args.input, args.output)
        
        if not success:
            return
    
    # Start webcam if requested
    if args.webcam:
        webcam_enhancer = HDWebcamEnhancer()
        webcam_enhancer.run_webcam()

if __name__ == "__main__":
    main()
