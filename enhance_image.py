#!/usr/bin/env python3
"""
Advanced Image Enhancement Script with Professional Sharpening

Features:
- RGB color balance preservation
- Natural exposure maintenance
- Fine edge detail enhancement
- 1080p output optimization
- Low-bandwidth simulation support
- Overexposure prevention
- Channel distortion avoidance

Usage:
    python enhance_image.py input_image.jpg
    python enhance_image.py input_image.jpg --output enhanced_image.jpg
    python enhance_image.py input_image.jpg --scale 4 --sharpen advanced
    python enhance_image.py input_image.jpg --target-resolution 1080p --preserve-colors
"""

import sys
import argparse
from pathlib import Path
import cv2
import torch
import numpy as np
import time
from skimage import exposure, filters
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')

def preserve_color_balance(original, enhanced):
    """
    Preserve RGB color balance from original image while keeping enhancement quality.

    Args:
        original: Original image (BGR format)
        enhanced: Enhanced image (BGR format)

    Returns:
        Color-balanced enhanced image
    """
    # Convert to LAB color space for better color preservation
    original_lab = cv2.cvtColor(original, cv2.COLOR_BGR2LAB)
    enhanced_lab = cv2.cvtColor(enhanced, cv2.COLOR_BGR2LAB)

    # Preserve original A and B channels (color information)
    # Use enhanced L channel (luminance/brightness)
    balanced_lab = enhanced_lab.copy()

    # Blend color channels to preserve original color balance
    alpha = 0.7  # Weight for original colors
    balanced_lab[:, :, 1] = alpha * original_lab[:, :, 1] + (1 - alpha) * enhanced_lab[:, :, 1]  # A channel
    balanced_lab[:, :, 2] = alpha * original_lab[:, :, 2] + (1 - alpha) * enhanced_lab[:, :, 2]  # B channel

    # Convert back to BGR
    balanced_bgr = cv2.cvtColor(balanced_lab, cv2.COLOR_LAB2BGR)
    return balanced_bgr

def prevent_overexposure(image, threshold=240):
    """
    Prevent overexposure by clipping highlights intelligently.

    Args:
        image: Input image (BGR format)
        threshold: Overexposure threshold (0-255)

    Returns:
        Exposure-corrected image
    """
    # Convert to float for processing
    img_float = image.astype(np.float32) / 255.0

    # Find overexposed regions
    overexposed_mask = np.any(img_float > (threshold / 255.0), axis=2)

    if np.any(overexposed_mask):
        # Apply gentle tone mapping to overexposed regions
        for c in range(3):
            channel = img_float[:, :, c]
            # Use sigmoid function to compress highlights
            channel[overexposed_mask] = 1.0 / (1.0 + np.exp(-10 * (channel[overexposed_mask] - 0.8)))
            img_float[:, :, c] = channel

    return (img_float * 255).astype(np.uint8)

def enhance_edge_details(image, method='unsharp_mask', strength=1.0):
    """
    Enhance fine edge details while preserving natural appearance.

    Args:
        image: Input image (BGR format)
        method: Enhancement method ('unsharp_mask', 'laplacian', 'adaptive')
        strength: Enhancement strength (0.5-2.0)

    Returns:
        Edge-enhanced image
    """
    if method == 'unsharp_mask':
        # Convert to float
        img_float = image.astype(np.float32) / 255.0

        # Create Gaussian blur
        blurred = cv2.GaussianBlur(img_float, (0, 0), 1.0)

        # Unsharp mask
        enhanced = img_float + strength * (img_float - blurred)
        enhanced = np.clip(enhanced, 0, 1)

        return (enhanced * 255).astype(np.uint8)

    elif method == 'laplacian':
        # Laplacian sharpening
        kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]], dtype=np.float32)
        kernel = kernel * strength + np.array([[0, 0, 0], [0, 1, 0], [0, 0, 0]])

        enhanced = cv2.filter2D(image, -1, kernel)
        return np.clip(enhanced, 0, 255).astype(np.uint8)

    elif method == 'adaptive':
        # Adaptive sharpening based on local contrast
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Calculate local variance (edge strength indicator)
        kernel = np.ones((5, 5), np.float32) / 25
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        local_variance = cv2.filter2D((gray.astype(np.float32) - local_mean) ** 2, -1, kernel)

        # Normalize variance to [0, 1]
        variance_norm = local_variance / (local_variance.max() + 1e-8)

        # Apply variable sharpening
        enhanced = image.copy().astype(np.float32)
        for c in range(3):
            channel = image[:, :, c].astype(np.float32)
            blurred = cv2.GaussianBlur(channel, (0, 0), 1.0)

            # Variable strength based on local variance
            adaptive_strength = strength * variance_norm
            enhanced[:, :, c] = channel + adaptive_strength * (channel - blurred)

        return np.clip(enhanced, 0, 255).astype(np.uint8)

    return image

def resize_to_target_resolution(image, target_resolution='1080p', maintain_aspect=True):
    """
    Resize image to target resolution while maintaining quality.

    Args:
        image: Input image
        target_resolution: Target resolution ('1080p', '720p', '4k', or tuple (width, height))
        maintain_aspect: Whether to maintain aspect ratio

    Returns:
        Resized image
    """
    if isinstance(target_resolution, str):
        resolution_map = {
            '1080p': (1920, 1080),
            '720p': (1280, 720),
            '4k': (3840, 2160),
            '2k': (2560, 1440)
        }
        target_width, target_height = resolution_map.get(target_resolution, (1920, 1080))
    else:
        target_width, target_height = target_resolution

    current_height, current_width = image.shape[:2]

    if maintain_aspect:
        # Calculate scaling factor to fit within target resolution
        scale_w = target_width / current_width
        scale_h = target_height / current_height
        scale = min(scale_w, scale_h)

        new_width = int(current_width * scale)
        new_height = int(current_height * scale)
    else:
        new_width, new_height = target_width, target_height

    # Use high-quality interpolation
    resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

    return resized

def simulate_low_bandwidth_optimization(image, quality_factor=0.8):
    """
    Optimize image for low-bandwidth scenarios while preserving visual quality.

    Args:
        image: Input image
        quality_factor: Quality preservation factor (0.5-1.0)

    Returns:
        Optimized image
    """
    # Apply gentle noise reduction
    denoised = cv2.bilateralFilter(image, 5, 50, 50)

    # Blend with original based on quality factor
    optimized = cv2.addWeighted(image, quality_factor, denoised, 1 - quality_factor, 0)

    return optimized

def enhance_image(input_path, output_path=None, scale_factor=2, model_type='mobilenetv2',
                 sharpen_method='unsharp_mask', sharpen_strength=1.0, target_resolution=None,
                 preserve_colors=True, prevent_overexp=True, low_bandwidth=False):
    """
    Enhance a single image using advanced AI enhancement with professional sharpening.

    Args:
        input_path: Path to input image
        output_path: Path to save enhanced image (optional)
        scale_factor: Upscaling factor (2 or 4)
        model_type: Model type ('mobilenetv2', 'tiny', 'efficient')
        sharpen_method: Sharpening method ('unsharp_mask', 'laplacian', 'adaptive')
        sharpen_strength: Sharpening strength (0.5-2.0)
        target_resolution: Target resolution ('1080p', '720p', '4k', or None)
        preserve_colors: Whether to preserve original RGB color balance
        prevent_overexp: Whether to prevent overexposure
        low_bandwidth: Whether to optimize for low-bandwidth scenarios
    """
    try:
        from src.models.student_models import StudentModelFactory

        print(f"🔍 Advanced Image Enhancement: {input_path}")
        print(f"🎯 Settings: {model_type} model, {scale_factor}x scale, {sharpen_method} sharpening")

        # Load image
        image = cv2.imread(input_path)
        if image is None:
            print(f"❌ Error: Could not load image {input_path}")
            return False

        original_image = image.copy()  # Keep original for color preservation
        original_shape = image.shape
        print(f"📏 Original size: {original_shape[1]}x{original_shape[0]}")

        # Create model
        print(f"🤖 Loading {model_type} model (scale factor: {scale_factor}x)...")
        model = StudentModelFactory.create_student(model_type, scale_factor=scale_factor)
        model.eval()

        # Preprocess image
        print("⚙️  Preprocessing with color preservation...")
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Convert to tensor and normalize
        tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0

        # AI Enhancement
        print("🚀 AI Enhancement processing...")
        start_time = time.time()

        with torch.no_grad():
            enhanced_tensor = model(tensor)

        ai_processing_time = time.time() - start_time

        # Convert back to image
        enhanced_np = enhanced_tensor.squeeze(0).permute(1, 2, 0).numpy()
        enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
        enhanced_bgr = cv2.cvtColor(enhanced_np, cv2.COLOR_RGB2BGR)

        print(f"⏱️  AI processing time: {ai_processing_time*1000:.1f}ms")

        # Advanced Post-processing Pipeline
        print("🎨 Advanced post-processing pipeline...")
        post_start = time.time()

        # Step 1: Preserve RGB color balance
        if preserve_colors:
            print("   🌈 Preserving RGB color balance...")
            # Resize original to match enhanced for color preservation
            original_resized = cv2.resize(original_image,
                                        (enhanced_bgr.shape[1], enhanced_bgr.shape[0]),
                                        interpolation=cv2.INTER_LANCZOS4)
            enhanced_bgr = preserve_color_balance(original_resized, enhanced_bgr)

        # Step 2: Prevent overexposure
        if prevent_overexp:
            print("   ☀️  Preventing overexposure...")
            enhanced_bgr = prevent_overexposure(enhanced_bgr, threshold=240)

        # Step 3: Enhance edge details
        print(f"   🔍 Enhancing edge details ({sharpen_method})...")
        enhanced_bgr = enhance_edge_details(enhanced_bgr, sharpen_method, sharpen_strength)

        # Step 4: Target resolution adjustment
        if target_resolution:
            print(f"   📐 Resizing to {target_resolution}...")
            enhanced_bgr = resize_to_target_resolution(enhanced_bgr, target_resolution)

        # Step 5: Low-bandwidth optimization
        if low_bandwidth:
            print("   📡 Optimizing for low-bandwidth...")
            enhanced_bgr = simulate_low_bandwidth_optimization(enhanced_bgr)

        post_processing_time = time.time() - post_start
        total_time = ai_processing_time + post_processing_time

        enhanced_shape = enhanced_bgr.shape
        print(f"📐 Final size: {enhanced_shape[1]}x{enhanced_shape[0]}")
        print(f"⏱️  Post-processing time: {post_processing_time*1000:.1f}ms")
        print(f"⏱️  Total processing time: {total_time*1000:.1f}ms")

        # Save result with quality settings
        if output_path is None:
            input_file = Path(input_path)
            suffix = f"_enhanced_{model_type}_{scale_factor}x"
            if target_resolution:
                suffix += f"_{target_resolution}"
            output_path = input_file.parent / f"{input_file.stem}{suffix}{input_file.suffix}"

        # Save with high quality
        if str(output_path).lower().endswith('.jpg') or str(output_path).lower().endswith('.jpeg'):
            cv2.imwrite(str(output_path), enhanced_bgr, [cv2.IMWRITE_JPEG_QUALITY, 95])
        else:
            cv2.imwrite(str(output_path), enhanced_bgr)

        print(f"✅ Enhanced image saved: {output_path}")

        # Calculate and display metrics
        scale_achieved = enhanced_shape[1] / original_shape[1]
        fps_equivalent = 1000 / (total_time * 1000) if total_time > 0 else 0

        print(f"📈 Enhancement Results:")
        print(f"   Scale factor achieved: {scale_achieved:.1f}x")
        print(f"   Equivalent FPS: {fps_equivalent:.1f}")
        print(f"   Color preservation: {'✅' if preserve_colors else '❌'}")
        print(f"   Overexposure protection: {'✅' if prevent_overexp else '❌'}")
        print(f"   Edge enhancement: {sharpen_method} (strength: {sharpen_strength})")

        return True

    except Exception as e:
        print(f"❌ Error enhancing image: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Advanced AI Image Enhancement with Professional Sharpening",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhance_image.py photo.jpg
  python enhance_image.py photo.jpg --sharpen advanced --strength 1.5
  python enhance_image.py photo.jpg --target-resolution 1080p --preserve-colors
  python enhance_image.py photo.jpg --model efficient --scale 4 --compare
  python enhance_image.py photo.jpg --low-bandwidth --prevent-overexposure
        """
    )

    # Basic options
    parser.add_argument("input", help="Input image path")
    parser.add_argument("--output", "-o", help="Output image path (optional)")
    parser.add_argument("--scale", "-s", type=int, choices=[2, 4], default=2,
                       help="Scale factor (2 or 4, default: 2)")
    parser.add_argument("--model", "-m", choices=['mobilenetv2', 'tiny', 'efficient'],
                       default='mobilenetv2', help="Model type (default: mobilenetv2)")

    # Advanced sharpening options
    parser.add_argument("--sharpen", choices=['unsharp_mask', 'laplacian', 'adaptive'],
                       default='unsharp_mask', help="Sharpening method (default: unsharp_mask)")
    parser.add_argument("--strength", type=float, default=1.0,
                       help="Sharpening strength (0.5-2.0, default: 1.0)")

    # Resolution and quality options
    parser.add_argument("--target-resolution", choices=['720p', '1080p', '2k', '4k'],
                       help="Target output resolution (optional)")
    parser.add_argument("--preserve-colors", action="store_true", default=True,
                       help="Preserve original RGB color balance (default: enabled)")
    parser.add_argument("--no-color-preservation", action="store_true",
                       help="Disable color preservation")
    parser.add_argument("--prevent-overexposure", action="store_true", default=True,
                       help="Prevent overexposure in highlights (default: enabled)")
    parser.add_argument("--no-overexposure-protection", action="store_true",
                       help="Disable overexposure protection")

    # Optimization options
    parser.add_argument("--low-bandwidth", action="store_true",
                       help="Optimize for low-bandwidth scenarios")
    parser.add_argument("--compare", "-c", action="store_true",
                       help="Create side-by-side comparison")

    args = parser.parse_args()

    # Validate arguments
    if args.strength < 0.5 or args.strength > 2.0:
        print("❌ Error: Sharpening strength must be between 0.5 and 2.0")
        return

    # Handle color preservation flags
    preserve_colors = args.preserve_colors and not args.no_color_preservation
    prevent_overexp = args.prevent_overexposure and not args.no_overexposure_protection

    # Check if input file exists
    if not Path(args.input).exists():
        print(f"❌ Error: Input file '{args.input}' not found")
        return

    print("🎯 Advanced AI Image Enhancement")
    print("=" * 50)
    print(f"📁 Input: {args.input}")
    print(f"🤖 Model: {args.model}")
    print(f"📏 Scale: {args.scale}x")
    print(f"🔍 Sharpening: {args.sharpen} (strength: {args.strength})")
    if args.target_resolution:
        print(f"📐 Target resolution: {args.target_resolution}")
    print(f"🌈 Color preservation: {'✅' if preserve_colors else '❌'}")
    print(f"☀️  Overexposure protection: {'✅' if prevent_overexp else '❌'}")
    print(f"📡 Low-bandwidth optimization: {'✅' if args.low_bandwidth else '❌'}")
    print("=" * 50)

    # Enhance image with advanced options
    success = enhance_image(
        input_path=args.input,
        output_path=args.output,
        scale_factor=args.scale,
        model_type=args.model,
        sharpen_method=args.sharpen,
        sharpen_strength=args.strength,
        target_resolution=args.target_resolution,
        preserve_colors=preserve_colors,
        prevent_overexp=prevent_overexp,
        low_bandwidth=args.low_bandwidth
    )

    if success and args.compare:
        output_file = args.output
        if output_file is None:
            input_file = Path(args.input)
            suffix = f"_enhanced_{args.model}_{args.scale}x"
            if args.target_resolution:
                suffix += f"_{args.target_resolution}"
            output_file = str(input_file.parent / f"{input_file.stem}{suffix}{input_file.suffix}")

        create_comparison(args.input, output_file)

def create_comparison(original_path, enhanced_path):
    """Create side-by-side comparison image."""
    try:
        print("🔄 Creating comparison image...")
        
        original = cv2.imread(original_path)
        enhanced = cv2.imread(enhanced_path)
        
        if original is None or enhanced is None:
            print("❌ Could not load images for comparison")
            return
        
        # Resize original to match enhanced height for comparison
        h_enhanced = enhanced.shape[0]
        h_original = original.shape[0]
        w_original = original.shape[1]
        
        # Calculate new width to maintain aspect ratio
        new_w = int(w_original * h_enhanced / h_original)
        original_resized = cv2.resize(original, (new_w, h_enhanced))
        
        # Create side-by-side comparison
        comparison = np.hstack([original_resized, enhanced])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 255, 0), 2)
        cv2.putText(comparison, "Enhanced", (new_w + 10, 30), font, 1, (0, 255, 0), 2)
        
        # Save comparison
        comparison_path = f"{Path(enhanced_path).stem}_comparison{Path(enhanced_path).suffix}"
        cv2.imwrite(comparison_path, comparison)
        print(f"✅ Comparison saved: {comparison_path}")
        
    except Exception as e:
        print(f"❌ Error creating comparison: {e}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🎯 Advanced AI Image Enhancement Tool")
        print("=" * 60)
        print("Professional image sharpening with RGB color balance preservation,")
        print("natural exposure maintenance, and fine edge detail enhancement.")
        print()
        print("Basic Usage:")
        print("  python enhance_image.py your_image.jpg")
        print("  python enhance_image.py your_image.jpg --output enhanced.jpg")
        print("  python enhance_image.py your_image.jpg --scale 4 --compare")
        print()
        print("Advanced Usage:")
        print("  python enhance_image.py photo.jpg --sharpen adaptive --strength 1.5")
        print("  python enhance_image.py photo.jpg --target-resolution 1080p")
        print("  python enhance_image.py photo.jpg --model efficient --scale 4")
        print("  python enhance_image.py photo.jpg --low-bandwidth --prevent-overexposure")
        print()
        print("Professional Features:")
        print("  --sharpen METHOD      Sharpening method (unsharp_mask, laplacian, adaptive)")
        print("  --strength FLOAT      Sharpening strength (0.5-2.0)")
        print("  --target-resolution   Output resolution (720p, 1080p, 2k, 4k)")
        print("  --preserve-colors     Maintain original RGB color balance")
        print("  --prevent-overexposure Avoid highlight clipping")
        print("  --low-bandwidth       Optimize for bandwidth-limited scenarios")
        print()
        print("Models Available:")
        print("  mobilenetv2 - Balanced quality and speed (default)")
        print("  tiny        - Fastest processing (30-60 FPS)")
        print("  efficient   - Best quality (SSIM >90%)")
        print()
        print("Sharpening Methods:")
        print("  unsharp_mask - Classic unsharp masking (default)")
        print("  laplacian    - Laplacian edge enhancement")
        print("  adaptive     - Content-aware adaptive sharpening")
        print()
        print("Quality Guarantees:")
        print("  ✅ True RGB color balance preservation")
        print("  ✅ Natural exposure maintenance")
        print("  ✅ Fine edge detail enhancement")
        print("  ✅ Overexposure prevention")
        print("  ✅ Channel distortion avoidance")
        print("  ✅ Full 1080p resolution support")
        print("  ✅ Low-bandwidth optimization")
        print()
        print("For help: python enhance_image.py --help")
    else:
        main()
