#!/usr/bin/env python3
"""
Simple Image Enhancement Script

Usage:
    python enhance_image.py input_image.jpg
    python enhance_image.py input_image.jpg --output enhanced_image.jpg
    python enhance_image.py input_image.jpg --scale 4
"""

import sys
import argparse
from pathlib import Path
import cv2
import torch
import numpy as np
import time

# Add src to path
sys.path.append('src')

def enhance_image(input_path, output_path=None, scale_factor=2, model_type='mobilenetv2'):
    """
    Enhance a single image using the trained model.
    
    Args:
        input_path: Path to input image
        output_path: Path to save enhanced image (optional)
        scale_factor: Upscaling factor (2 or 4)
        model_type: Model type ('mobilenetv2', 'tiny', 'efficient')
    """
    try:
        from src.models.student_models import StudentModelFactory
        
        print(f"🔍 Enhancing image: {input_path}")
        
        # Load image
        image = cv2.imread(input_path)
        if image is None:
            print(f"❌ Error: Could not load image {input_path}")
            return False
        
        original_shape = image.shape
        print(f"📏 Original size: {original_shape[1]}x{original_shape[0]}")
        
        # Create model
        print(f"🤖 Loading {model_type} model (scale factor: {scale_factor}x)...")
        model = StudentModelFactory.create_student(model_type, scale_factor=scale_factor)
        model.eval()
        
        # Preprocess image
        print("⚙️  Preprocessing...")
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to tensor and normalize
        tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        
        # Enhance image
        print("🚀 Enhancing image...")
        start_time = time.time()
        
        with torch.no_grad():
            enhanced_tensor = model(tensor)
        
        processing_time = time.time() - start_time
        
        # Postprocess
        print("🎨 Postprocessing...")
        enhanced_np = enhanced_tensor.squeeze(0).permute(1, 2, 0).numpy()
        enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
        enhanced_bgr = cv2.cvtColor(enhanced_np, cv2.COLOR_RGB2BGR)
        
        enhanced_shape = enhanced_bgr.shape
        print(f"📐 Enhanced size: {enhanced_shape[1]}x{enhanced_shape[0]}")
        print(f"⏱️  Processing time: {processing_time*1000:.1f}ms")
        
        # Save result
        if output_path is None:
            input_file = Path(input_path)
            output_path = input_file.parent / f"{input_file.stem}_enhanced{input_file.suffix}"
        
        cv2.imwrite(str(output_path), enhanced_bgr)
        print(f"✅ Enhanced image saved: {output_path}")
        
        # Show improvement
        scale_achieved = enhanced_shape[1] / original_shape[1]
        print(f"📈 Scale factor achieved: {scale_achieved:.1f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enhancing image: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Enhance custom images with AI")
    parser.add_argument("input", help="Input image path")
    parser.add_argument("--output", "-o", help="Output image path (optional)")
    parser.add_argument("--scale", "-s", type=int, choices=[2, 4], default=2, 
                       help="Scale factor (2 or 4, default: 2)")
    parser.add_argument("--model", "-m", choices=['mobilenetv2', 'tiny', 'efficient'], 
                       default='mobilenetv2', help="Model type (default: mobilenetv2)")
    parser.add_argument("--compare", "-c", action="store_true", 
                       help="Create side-by-side comparison")
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not Path(args.input).exists():
        print(f"❌ Error: Input file '{args.input}' not found")
        return
    
    print("🎯 AI Image Enhancement")
    print("=" * 40)
    
    # Enhance image
    success = enhance_image(args.input, args.output, args.scale, args.model)
    
    if success and args.compare:
        create_comparison(args.input, args.output or f"{Path(args.input).stem}_enhanced{Path(args.input).suffix}")

def create_comparison(original_path, enhanced_path):
    """Create side-by-side comparison image."""
    try:
        print("🔄 Creating comparison image...")
        
        original = cv2.imread(original_path)
        enhanced = cv2.imread(enhanced_path)
        
        if original is None or enhanced is None:
            print("❌ Could not load images for comparison")
            return
        
        # Resize original to match enhanced height for comparison
        h_enhanced = enhanced.shape[0]
        h_original = original.shape[0]
        w_original = original.shape[1]
        
        # Calculate new width to maintain aspect ratio
        new_w = int(w_original * h_enhanced / h_original)
        original_resized = cv2.resize(original, (new_w, h_enhanced))
        
        # Create side-by-side comparison
        comparison = np.hstack([original_resized, enhanced])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 255, 0), 2)
        cv2.putText(comparison, "Enhanced", (new_w + 10, 30), font, 1, (0, 255, 0), 2)
        
        # Save comparison
        comparison_path = f"{Path(enhanced_path).stem}_comparison{Path(enhanced_path).suffix}"
        cv2.imwrite(comparison_path, comparison)
        print(f"✅ Comparison saved: {comparison_path}")
        
    except Exception as e:
        print(f"❌ Error creating comparison: {e}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🎯 AI Image Enhancement Tool")
        print("=" * 40)
        print("Usage:")
        print("  python enhance_image.py your_image.jpg")
        print("  python enhance_image.py your_image.jpg --output enhanced.jpg")
        print("  python enhance_image.py your_image.jpg --scale 4")
        print("  python enhance_image.py your_image.jpg --compare")
        print()
        print("Examples:")
        print("  python enhance_image.py photo.jpg")
        print("  python enhance_image.py low_res.png --scale 4 --compare")
        print("  python enhance_image.py selfie.jpg --model tiny --output hd_selfie.jpg")
        print()
        print("Models available:")
        print("  mobilenetv2 - Balanced quality and speed (default)")
        print("  tiny        - Fastest processing")
        print("  efficient   - Best quality")
    else:
        main()
