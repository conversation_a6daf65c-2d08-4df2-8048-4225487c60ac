#!/usr/bin/env python3
"""
Batch Image Enhancement Script

Process multiple images in a directory with AI enhancement.

Usage:
    python batch_enhance.py input_folder/
    python batch_enhance.py input_folder/ --output enhanced_folder/
    python batch_enhance.py input_folder/ --scale 4 --model efficient
"""

import sys
import argparse
from pathlib import Path
import cv2
import torch
import numpy as np
import time
from tqdm import tqdm

# Add src to path
sys.path.append('src')

def batch_enhance_images(input_dir, output_dir=None, scale_factor=2, model_type='mobilenetv2'):
    """
    Enhance all images in a directory.
    
    Args:
        input_dir: Input directory containing images
        output_dir: Output directory (optional)
        scale_factor: Upscaling factor
        model_type: Model type to use
    """
    try:
        from src.models.student_models import StudentModelFactory
        
        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ Error: Input directory '{input_dir}' not found")
            return False
        
        # Set output directory
        if output_dir is None:
            output_path = input_path.parent / f"{input_path.name}_enhanced"
        else:
            output_path = Path(output_dir)
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Input directory: {input_path}")
        print(f"📁 Output directory: {output_path}")
        
        # Find all image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"❌ No image files found in {input_dir}")
            return False
        
        print(f"🖼️  Found {len(image_files)} images to process")
        
        # Load model
        print(f"🤖 Loading {model_type} model (scale factor: {scale_factor}x)...")
        model = StudentModelFactory.create_student(model_type, scale_factor=scale_factor)
        model.eval()
        
        # Process images
        successful = 0
        failed = 0
        total_time = 0
        
        print("🚀 Processing images...")
        
        for image_file in tqdm(image_files, desc="Enhancing"):
            try:
                # Load image
                image = cv2.imread(str(image_file))
                if image is None:
                    print(f"⚠️  Skipping {image_file.name}: Could not load")
                    failed += 1
                    continue
                
                # Preprocess
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
                
                # Enhance
                start_time = time.time()
                with torch.no_grad():
                    enhanced_tensor = model(tensor)
                process_time = time.time() - start_time
                total_time += process_time
                
                # Postprocess
                enhanced_np = enhanced_tensor.squeeze(0).permute(1, 2, 0).numpy()
                enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
                enhanced_bgr = cv2.cvtColor(enhanced_np, cv2.COLOR_RGB2BGR)
                
                # Save
                output_file = output_path / f"{image_file.stem}_enhanced{image_file.suffix}"
                cv2.imwrite(str(output_file), enhanced_bgr)
                
                successful += 1
                
            except Exception as e:
                print(f"❌ Error processing {image_file.name}: {e}")
                failed += 1
        
        # Summary
        print("\n📊 Batch Processing Complete!")
        print("=" * 40)
        print(f"✅ Successfully processed: {successful} images")
        print(f"❌ Failed: {failed} images")
        print(f"⏱️  Total time: {total_time:.1f}s")
        print(f"📈 Average time per image: {total_time/len(image_files):.2f}s")
        print(f"🚀 Processing speed: {len(image_files)/total_time:.1f} images/sec")
        print(f"📁 Enhanced images saved in: {output_path}")
        
        return successful > 0
        
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")
        return False

def create_batch_comparison(input_dir, output_dir):
    """Create comparison images for batch processing."""
    try:
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        comparison_path = output_path.parent / f"{output_path.name}_comparisons"
        comparison_path.mkdir(exist_ok=True)
        
        print(f"🔄 Creating comparison images in: {comparison_path}")
        
        # Find enhanced images
        enhanced_files = list(output_path.glob("*_enhanced.*"))
        
        for enhanced_file in tqdm(enhanced_files, desc="Creating comparisons"):
            # Find original file
            original_name = enhanced_file.name.replace("_enhanced", "")
            original_file = input_path / original_name
            
            if not original_file.exists():
                continue
            
            # Load images
            original = cv2.imread(str(original_file))
            enhanced = cv2.imread(str(enhanced_file))
            
            if original is None or enhanced is None:
                continue
            
            # Resize original to match enhanced height
            h_enhanced = enhanced.shape[0]
            h_original = original.shape[0]
            w_original = original.shape[1]
            new_w = int(w_original * h_enhanced / h_original)
            original_resized = cv2.resize(original, (new_w, h_enhanced))
            
            # Create comparison
            comparison = np.hstack([original_resized, enhanced])
            
            # Add labels
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(comparison, "Enhanced", (new_w + 10, 30), font, 1, (0, 255, 0), 2)
            
            # Save comparison
            comparison_file = comparison_path / f"{enhanced_file.stem}_comparison{enhanced_file.suffix}"
            cv2.imwrite(str(comparison_file), comparison)
        
        print(f"✅ Comparison images created in: {comparison_path}")
        
    except Exception as e:
        print(f"❌ Error creating comparisons: {e}")

def main():
    parser = argparse.ArgumentParser(description="Batch enhance multiple images with AI")
    parser.add_argument("input_dir", help="Input directory containing images")
    parser.add_argument("--output", "-o", help="Output directory (optional)")
    parser.add_argument("--scale", "-s", type=int, choices=[2, 4], default=2,
                       help="Scale factor (2 or 4, default: 2)")
    parser.add_argument("--model", "-m", choices=['mobilenetv2', 'tiny', 'efficient'],
                       default='mobilenetv2', help="Model type (default: mobilenetv2)")
    parser.add_argument("--compare", "-c", action="store_true",
                       help="Create side-by-side comparison images")
    
    args = parser.parse_args()
    
    print("🎯 AI Batch Image Enhancement")
    print("=" * 40)
    
    # Process images
    success = batch_enhance_images(args.input_dir, args.output, args.scale, args.model)
    
    if success and args.compare:
        output_dir = args.output or f"{Path(args.input_dir).name}_enhanced"
        create_batch_comparison(args.input_dir, output_dir)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🎯 AI Batch Image Enhancement Tool")
        print("=" * 40)
        print("Usage:")
        print("  python batch_enhance.py input_folder/")
        print("  python batch_enhance.py input_folder/ --output enhanced_folder/")
        print("  python batch_enhance.py input_folder/ --scale 4 --compare")
        print()
        print("Examples:")
        print("  python batch_enhance.py photos/")
        print("  python batch_enhance.py vacation_pics/ --scale 4")
        print("  python batch_enhance.py selfies/ --model efficient --compare")
        print()
        print("This will process all images in the folder and create enhanced versions.")
    else:
        main()
