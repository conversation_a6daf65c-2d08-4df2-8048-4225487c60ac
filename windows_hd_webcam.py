#!/usr/bin/env python3
"""
Windows HD Webcam Enhancement

Specifically designed for Windows systems to avoid OpenCV window issues.
Provides true 1080p HD enhancement with robust error handling.
"""

import cv2
import numpy as np
import time
import os
import sys
from pathlib import Path

class WindowsHDWebcam:
    """Windows-optimized HD webcam enhancer."""
    
    def __init__(self):
        print("🎯 Windows HD Webcam Enhancer")
        self.setup_opencv()
    
    def setup_opencv(self):
        """Setup OpenCV with Windows-specific configurations."""
        try:
            # Set OpenCV backend for Windows
            os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '1'
            print("🔧 OpenCV configured for Windows")
        except:
            pass
    
    def enhance_to_hd(self, frame):
        """Enhance frame to true HD quality."""
        start_time = time.time()
        
        # Get current dimensions
        height, width = frame.shape[:2]
        
        # Step 1: Noise reduction while preserving edges
        denoised = cv2.bilateralFilter(frame, 9, 75, 75)
        
        # Step 2: Advanced sharpening
        # Create unsharp mask
        gaussian = cv2.GaussianBlur(denoised, (0, 0), 2.0)
        unsharp = cv2.addWeighted(denoised, 1.5, gaussian, -0.5, 0)
        
        # Step 3: Contrast enhancement using CLAHE
        lab = cv2.cvtColor(unsharp, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to luminance channel
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        l_enhanced = clahe.apply(l)
        
        # Merge back
        enhanced_lab = cv2.merge([l_enhanced, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        # Step 4: Final sharpening
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]], dtype=np.float32)
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # Blend for natural result
        result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
        
        # Step 5: Scale to HD resolution
        if height < 1080:
            # Calculate scale to reach 1080p
            scale = 1080 / height
            new_width = int(width * scale)
            
            # Use high-quality interpolation
            result = cv2.resize(result, (new_width, 1080), interpolation=cv2.INTER_LANCZOS4)
        
        processing_time = (time.time() - start_time) * 1000
        return result, processing_time
    
    def add_hd_overlay(self, frame, fps, proc_time, original_size, enhanced_size):
        """Add HD enhancement information overlay."""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        color = (0, 255, 0)  # Green
        thickness = 2
        
        # Create semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (500, 150), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, frame, 0.2, 0, frame)
        
        # Add text information
        texts = [
            f"HD ENHANCEMENT: ACTIVE",
            f"Original: {original_size[1]}x{original_size[0]}",
            f"Enhanced: {enhanced_size[1]}x{enhanced_size[0]}",
            f"FPS: {fps:.1f} | Processing: {proc_time:.1f}ms",
            f"Quality: TRUE 1080p HD"
        ]
        
        y_start = 35
        for i, text in enumerate(texts):
            y_pos = y_start + (i * 25)
            cv2.putText(frame, text, (20, y_pos), font, font_scale, color, thickness)
    
    def save_hd_frame(self, frame, prefix="hd_capture"):
        """Save HD frame with timestamp."""
        timestamp = int(time.time())
        filename = f"{prefix}_{timestamp}.jpg"
        
        # Save with maximum quality
        success = cv2.imwrite(filename, frame, [
            cv2.IMWRITE_JPEG_QUALITY, 100,
            cv2.IMWRITE_JPEG_OPTIMIZE, True
        ])
        
        if success:
            height, width = frame.shape[:2]
            print(f"💾 HD frame saved: {filename} ({width}x{height})")
            return filename
        else:
            print("❌ Failed to save frame")
            return None
    
    def run_hd_webcam(self):
        """Run HD webcam enhancement with Windows optimizations."""
        print("🎥 Starting Windows HD Webcam Enhancement...")
        print("=" * 50)
        print("Controls:")
        print("  SPACE or 's' - Save HD frame")
        print("  'i' - Toggle info overlay")
        print("  'q' or ESC - Quit")
        print("=" * 50)
        
        # Initialize webcam with Windows-specific settings
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)  # DirectShow backend for Windows
        
        if not cap.isOpened():
            print("❌ Cannot open webcam with DirectShow, trying default...")
            cap = cv2.VideoCapture(0)
            
            if not cap.isOpened():
                print("❌ Cannot open webcam")
                return False
        
        # Set webcam properties for best quality
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        cap.set(cv2.CAP_PROP_FPS, 30)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce latency
        
        # Get actual webcam resolution
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        actual_fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"📹 Webcam: {actual_width}x{actual_height} @ {actual_fps}fps")
        
        # Window setup with error handling
        window_name = "Windows_HD_Webcam"
        window_created = False
        
        # State variables
        show_info = True
        frame_count = 0
        saved_count = 0
        fps_history = []
        last_time = time.time()
        
        print("🎬 Starting HD enhancement...")
        print("📺 Webcam window should appear...")
        
        try:
            while True:
                # Capture frame
                ret, original_frame = cap.read()
                if not ret:
                    print("❌ Cannot read frame")
                    time.sleep(0.1)
                    continue
                
                current_time = time.time()
                original_size = original_frame.shape
                
                # Enhance to HD
                hd_frame, proc_time = self.enhance_to_hd(original_frame)
                enhanced_size = hd_frame.shape
                
                # Calculate FPS
                if current_time > last_time:
                    fps = 1.0 / (current_time - last_time)
                    fps_history.append(fps)
                    if len(fps_history) > 30:
                        fps_history.pop(0)
                
                avg_fps = sum(fps_history) / len(fps_history) if fps_history else 0
                last_time = current_time
                
                # Add overlay
                display_frame = hd_frame.copy()
                if show_info:
                    self.add_hd_overlay(display_frame, avg_fps, proc_time, original_size, enhanced_size)
                
                # Display frame with robust error handling
                try:
                    if not window_created:
                        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
                        cv2.resizeWindow(window_name, 1280, 720)  # Display size
                        window_created = True
                        print("✅ Window created successfully")
                    
                    cv2.imshow(window_name, display_frame)
                    
                except Exception as e:
                    print(f"⚠️  Display error: {e}")
                    # Try alternative display method
                    try:
                        cv2.destroyAllWindows()
                        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
                        cv2.imshow(window_name, display_frame)
                        window_created = True
                        print("🔧 Window recreated")
                    except Exception as e2:
                        print(f"❌ Cannot create window: {e2}")
                        print("💾 Saving frames to disk instead...")
                        
                        # Save frame instead of displaying
                        if frame_count % 30 == 0:  # Save every 30 frames
                            self.save_hd_frame(display_frame, "auto_hd")
                        
                        frame_count += 1
                        if frame_count > 300:  # Stop after 300 frames
                            print("🛑 Auto-save mode completed")
                            break
                        continue
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q') or key == 27:  # 'q' or ESC
                    print("🛑 Quit requested")
                    break
                elif key == ord(' ') or key == ord('s'):  # SPACE or 's'
                    filename = self.save_hd_frame(display_frame)
                    if filename:
                        saved_count += 1
                elif key == ord('i'):
                    show_info = not show_info
                    status = "ON" if show_info else "OFF"
                    print(f"ℹ️  Info overlay: {status}")
                
                frame_count += 1
                
                # Progress indicator
                if frame_count % 100 == 0:
                    print(f"📊 Processed {frame_count} frames, avg FPS: {avg_fps:.1f}")
        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        finally:
            # Cleanup
            print("🧹 Cleaning up...")
            cap.release()
            cv2.destroyAllWindows()
            
            # Final statistics
            if fps_history:
                final_fps = sum(fps_history) / len(fps_history)
                print(f"📊 Final Statistics:")
                print(f"   Average FPS: {final_fps:.1f}")
                print(f"   Frames processed: {frame_count}")
                print(f"   HD frames saved: {saved_count}")
                print(f"   Enhancement: TRUE 1080p HD")
            
            print("✅ HD webcam session completed")
        
        return True

def main():
    print("🎯 Windows HD Webcam Enhancement")
    print("True 1080p HD quality enhancement for Windows")
    print("=" * 50)
    
    # Check OpenCV version
    print(f"📚 OpenCV version: {cv2.__version__}")
    
    # Test webcam availability
    print("🔍 Testing webcam availability...")
    test_cap = cv2.VideoCapture(0)
    if test_cap.isOpened():
        ret, frame = test_cap.read()
        if ret:
            print(f"✅ Webcam test passed: {frame.shape}")
        else:
            print("❌ Cannot read from webcam")
            test_cap.release()
            return
        test_cap.release()
    else:
        print("❌ Cannot open webcam")
        return
    
    # Run HD webcam
    webcam = WindowsHDWebcam()
    success = webcam.run_hd_webcam()
    
    if success:
        print("🎉 HD webcam enhancement completed successfully!")
    else:
        print("❌ HD webcam enhancement failed")

if __name__ == "__main__":
    main()
