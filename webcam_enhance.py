#!/usr/bin/env python3
"""
Real-time Webcam Image Enhancement

Usage:
    python webcam_enhance.py                    # Default settings
    python webcam_enhance.py --model tiny       # Use tiny model for speed
    python webcam_enhance.py --scale 4          # 4x upscaling
    python webcam_enhance.py --fps 30           # Target 30 FPS
    python webcam_enhance.py --save-frames      # Save enhanced frames
"""

import sys
import argparse
import cv2
import torch
import numpy as np
import time
from pathlib import Path
from collections import deque
import threading
import queue

# Add src to path
sys.path.append('src')

class WebcamEnhancer:
    def __init__(self, model_type='mobilenetv2', scale_factor=2, target_fps=30, save_frames=False):
        """
        Initialize webcam enhancer.
        
        Args:
            model_type: Model type ('mobilenetv2', 'tiny', 'efficient')
            scale_factor: Upscaling factor (2 or 4)
            target_fps: Target FPS for processing
            save_frames: Whether to save enhanced frames
        """
        self.model_type = model_type
        self.scale_factor = scale_factor
        self.target_fps = target_fps
        self.save_frames = save_frames
        self.frame_interval = 1.0 / target_fps
        
        # Performance tracking
        self.fps_history = deque(maxlen=30)
        self.processing_times = deque(maxlen=30)
        
        # Frame saving
        self.frame_count = 0
        self.save_dir = Path("webcam_captures")
        if save_frames:
            self.save_dir.mkdir(exist_ok=True)
        
        # Threading for better performance
        self.frame_queue = queue.Queue(maxsize=2)
        self.result_queue = queue.Queue(maxsize=2)
        self.processing_thread = None
        self.running = False
        
        # Load model
        self.model = None
        self.load_model()
    
    def load_model(self):
        """Load the enhancement model."""
        try:
            from src.models.student_models import StudentModelFactory
            
            print(f"🤖 Loading {self.model_type} model (scale: {self.scale_factor}x)...")
            self.model = StudentModelFactory.create_student(
                self.model_type, 
                scale_factor=self.scale_factor
            )
            self.model.eval()
            print("✅ Model loaded successfully!")
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            sys.exit(1)
    
    def preprocess_frame(self, frame):
        """Preprocess frame for model input."""
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Convert to tensor and normalize
        tensor = torch.from_numpy(frame_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        return tensor
    
    def postprocess_frame(self, tensor):
        """Postprocess model output to frame."""
        # Convert back to numpy
        frame_np = tensor.squeeze(0).permute(1, 2, 0).numpy()
        frame_np = np.clip(frame_np * 255, 0, 255).astype(np.uint8)
        
        # Convert RGB to BGR for OpenCV
        frame_bgr = cv2.cvtColor(frame_np, cv2.COLOR_RGB2BGR)
        return frame_bgr
    
    def enhance_frame(self, frame):
        """Enhance a single frame."""
        try:
            start_time = time.time()
            
            # Preprocess
            tensor = self.preprocess_frame(frame)
            
            # Enhance
            with torch.no_grad():
                enhanced_tensor = self.model(tensor)
            
            # Postprocess
            enhanced_frame = self.postprocess_frame(enhanced_tensor)
            
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time * 1000)  # Convert to ms
            
            return enhanced_frame, processing_time
            
        except Exception as e:
            print(f"❌ Error enhancing frame: {e}")
            return frame, 0
    
    def processing_worker(self):
        """Worker thread for frame processing."""
        while self.running:
            try:
                # Get frame from queue
                frame = self.frame_queue.get(timeout=0.1)
                
                # Enhance frame
                enhanced_frame, proc_time = self.enhance_frame(frame)
                
                # Put result in queue
                if not self.result_queue.full():
                    self.result_queue.put((enhanced_frame, proc_time))
                
                self.frame_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Processing error: {e}")
    
    def add_overlay_info(self, frame, fps, processing_time, original_size, enhanced_size):
        """Add performance information overlay."""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        color = (0, 255, 0)  # Green
        thickness = 2
        
        # Background for text
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (400, 120), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Add text
        y_offset = 30
        texts = [
            f"FPS: {fps:.1f}",
            f"Processing: {processing_time:.1f}ms",
            f"Original: {original_size[1]}x{original_size[0]}",
            f"Enhanced: {enhanced_size[1]}x{enhanced_size[0]}",
            f"Model: {self.model_type} ({self.scale_factor}x)"
        ]
        
        for text in texts:
            cv2.putText(frame, text, (15, y_offset), font, font_scale, color, thickness)
            y_offset += 20
    
    def save_frame(self, original, enhanced):
        """Save original and enhanced frames."""
        if not self.save_frames:
            return
        
        try:
            timestamp = int(time.time() * 1000)
            
            # Save original
            orig_path = self.save_dir / f"original_{timestamp:013d}.jpg"
            cv2.imwrite(str(orig_path), original)
            
            # Save enhanced
            enh_path = self.save_dir / f"enhanced_{timestamp:013d}.jpg"
            cv2.imwrite(str(enh_path), enhanced)
            
            self.frame_count += 1
            
        except Exception as e:
            print(f"❌ Error saving frame: {e}")
    
    def run(self):
        """Main webcam enhancement loop."""
        print("🎥 Starting webcam enhancement...")
        print("Press 'q' to quit, 's' to save current frame, 'i' to toggle info")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Error: Could not open webcam")
            return
        
        # Set webcam properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Start processing thread
        self.running = True
        self.processing_thread = threading.Thread(target=self.processing_worker)
        self.processing_thread.start()
        
        show_info = True
        last_frame_time = time.time()
        enhanced_frame = None
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Error reading from webcam")
                    break
                
                current_time = time.time()
                
                # Add frame to processing queue if not full
                if not self.frame_queue.full():
                    self.frame_queue.put(frame.copy())
                
                # Get enhanced frame if available
                try:
                    enhanced_frame, proc_time = self.result_queue.get_nowait()
                except queue.Empty:
                    pass
                
                # Calculate FPS
                fps = 1.0 / (current_time - last_frame_time)
                self.fps_history.append(fps)
                last_frame_time = current_time
                
                # Display frames
                display_frame = enhanced_frame if enhanced_frame is not None else frame
                
                if show_info and enhanced_frame is not None:
                    avg_fps = sum(self.fps_history) / len(self.fps_history)
                    avg_proc_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
                    
                    self.add_overlay_info(
                        display_frame, avg_fps, avg_proc_time,
                        frame.shape, enhanced_frame.shape
                    )
                
                cv2.imshow('AI Enhanced Webcam', display_frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s') and enhanced_frame is not None:
                    self.save_frame(frame, enhanced_frame)
                    print(f"💾 Frame saved! Total frames: {self.frame_count}")
                elif key == ord('i'):
                    show_info = not show_info
                    print(f"ℹ️  Info overlay: {'ON' if show_info else 'OFF'}")
        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
        
        finally:
            # Cleanup
            self.running = False
            if self.processing_thread:
                self.processing_thread.join()
            
            cap.release()
            cv2.destroyAllWindows()
            
            # Print statistics
            if self.fps_history:
                avg_fps = sum(self.fps_history) / len(self.fps_history)
                print(f"📊 Average FPS: {avg_fps:.1f}")
            
            if self.processing_times:
                avg_proc_time = sum(self.processing_times) / len(self.processing_times)
                print(f"⏱️  Average processing time: {avg_proc_time:.1f}ms")
            
            if self.save_frames:
                print(f"💾 Saved {self.frame_count} frame pairs to {self.save_dir}")

def main():
    parser = argparse.ArgumentParser(description="Real-time webcam image enhancement")
    parser.add_argument("--model", "-m", choices=['mobilenetv2', 'tiny', 'efficient'], 
                       default='mobilenetv2', help="Model type (default: mobilenetv2)")
    parser.add_argument("--scale", "-s", type=int, choices=[2, 4], default=2,
                       help="Scale factor (2 or 4, default: 2)")
    parser.add_argument("--fps", "-f", type=int, default=30,
                       help="Target FPS (default: 30)")
    parser.add_argument("--save-frames", action="store_true",
                       help="Save enhanced frames to disk")
    
    args = parser.parse_args()
    
    print("🎯 AI Webcam Enhancement")
    print("=" * 40)
    print(f"Model: {args.model}")
    print(f"Scale: {args.scale}x")
    print(f"Target FPS: {args.fps}")
    print(f"Save frames: {args.save_frames}")
    print()
    
    # Create and run enhancer
    enhancer = WebcamEnhancer(
        model_type=args.model,
        scale_factor=args.scale,
        target_fps=args.fps,
        save_frames=args.save_frames
    )
    
    enhancer.run()

if __name__ == "__main__":
    main()
