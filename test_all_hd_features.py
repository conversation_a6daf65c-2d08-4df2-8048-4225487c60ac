#!/usr/bin/env python3
"""
Test All HD Enhancement Features

Comprehensive testing script for all HD enhancement functionality.
"""

import cv2
import numpy as np
import subprocess
import sys
from pathlib import Path
import time

def create_test_images():
    """Create test images for enhancement."""
    print("🎨 Creating test images...")
    
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    # Test image 1: Portrait with fine details
    portrait = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Background gradient
    for i in range(480):
        for j in range(640):
            portrait[i, j] = [200 - i//3, 220 - j//4, 240 - i//4]
    
    # Face
    cv2.ellipse(portrait, (320, 200), (80, 100), 0, 0, 360, (220, 180, 160), -1)
    cv2.circle(portrait, (300, 180), 8, (50, 50, 50), -1)  # Eyes
    cv2.circle(portrait, (340, 180), 8, (50, 50, 50), -1)
    cv2.ellipse(portrait, (320, 220), (15, 5), 0, 0, 360, (180, 120, 100), -1)  # Mouth
    
    # Hair texture
    for i in range(100, 300):
        for j in range(250, 390):
            if np.random.random() > 0.7:
                portrait[i, j] = [80, 60, 40]
    
    cv2.imwrite(str(test_dir / "portrait_test.jpg"), portrait)
    
    # Test image 2: Landscape with details
    landscape = np.zeros((360, 640, 3), dtype=np.uint8)
    
    # Sky
    for i in range(150):
        landscape[i, :] = [255 - i, 240 - i//2, 200 - i//3]
    
    # Mountains
    points = np.array([[0, 150], [150, 80], [300, 120], [450, 60], [640, 100], [640, 150], [0, 150]], np.int32)
    cv2.fillPoly(landscape, [points], (100, 120, 80))
    
    # Trees
    for x in range(50, 600, 60):
        cv2.rectangle(landscape, (x-3, 150), (x+3, 200), (80, 60, 40), -1)
        cv2.circle(landscape, (x, 140), 20, (60, 120, 40), -1)
    
    # Ground texture
    for i in range(200, 360):
        for j in range(640):
            if np.random.random() > 0.8:
                landscape[i, j] = [40 + np.random.randint(0, 20), 80 + np.random.randint(0, 20), 20]
            else:
                landscape[i, j] = [60, 100, 40]
    
    cv2.imwrite(str(test_dir / "landscape_test.jpg"), landscape)
    
    # Test image 3: Text and fine details
    text_img = np.ones((480, 640, 3), dtype=np.uint8) * 240
    
    font = cv2.FONT_HERSHEY_SIMPLEX
    texts = [
        ("ULTRA HD TEST", 2.0, (50, 100)),
        ("Fine Detail Enhancement", 1.2, (50, 180)),
        ("1080p+ Resolution Target", 1.0, (50, 240)),
        ("Superior Quality Algorithm", 0.8, (50, 300)),
        ("Edge Preservation & Sharpening", 0.6, (50, 360)),
        ("Multi-scale Enhancement", 0.5, (50, 420))
    ]
    
    for text, scale, pos in texts:
        cv2.putText(text_img, text, pos, font, scale, (0, 0, 0), 3)
        cv2.putText(text_img, text, (pos[0]+2, pos[1]+2), font, scale, (100, 100, 100), 2)
    
    # Fine grid
    for i in range(0, 480, 15):
        cv2.line(text_img, (0, i), (640, i), (200, 200, 200), 1)
    for j in range(0, 640, 15):
        cv2.line(text_img, (j, 0), (j, 480), (200, 200, 200), 1)
    
    cv2.imwrite(str(test_dir / "text_detail_test.jpg"), text_img)
    
    print(f"✅ Test images created in {test_dir}")
    return test_dir

def run_command(command, description):
    """Run a command and show results."""
    print(f"\n🔧 {description}")
    print(f"Command: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr and result.returncode != 0:
            print(f"Error: {result.stderr}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Command failed: {e}")
        return False

def test_opencv_installation():
    """Test OpenCV installation and suggest fixes."""
    print("🔍 Testing OpenCV installation...")
    
    try:
        import cv2
        print(f"✅ OpenCV version: {cv2.__version__}")
        
        # Test basic functionality
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        print("✅ Basic OpenCV operations work")
        
        # Test webcam
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f"✅ Webcam test passed: {frame.shape}")
            else:
                print("⚠️  Webcam can open but cannot read frames")
            cap.release()
        else:
            print("⚠️  Cannot open webcam")
        
        # Test GUI functions
        try:
            cv2.namedWindow("test", cv2.WINDOW_NORMAL)
            cv2.destroyWindow("test")
            print("✅ OpenCV GUI functions work")
            return True
        except Exception as e:
            print(f"❌ OpenCV GUI not supported: {e}")
            print("🔧 Recommendation: Use ultimate_hd_enhancer.py (no GUI needed)")
            return False
            
    except ImportError as e:
        print(f"❌ OpenCV not installed: {e}")
        return False

def main():
    print("🎯 HD Enhancement - Complete Test Suite")
    print("=" * 60)
    
    # Test OpenCV
    opencv_gui_works = test_opencv_installation()
    
    # Create test images
    test_dir = create_test_images()
    test_images = list(test_dir.glob("*.jpg"))
    
    print(f"\n📋 Available Test Commands:")
    print("=" * 60)
    
    print("\n1️⃣  SINGLE IMAGE ENHANCEMENT (Recommended)")
    print("   Superior quality, works without GUI issues")
    for img in test_images:
        print(f"   python ultimate_hd_enhancer.py --image {img}")
    
    print(f"\n   Custom image:")
    print(f"   python ultimate_hd_enhancer.py --image your_photo.jpg")
    print(f"   python ultimate_hd_enhancer.py --image your_photo.jpg --output ./results")
    
    print("\n2️⃣  WEBCAM HD ENHANCEMENT")
    print("   Captures and enhances webcam frames to HD")
    print("   python ultimate_hd_enhancer.py --webcam")
    print("   python ultimate_hd_enhancer.py --webcam --duration 30 --interval 2")
    
    print("\n3️⃣  BATCH PROCESSING")
    print("   Process multiple images at once")
    print(f"   python ultimate_hd_enhancer.py --batch {test_dir}")
    print("   python ultimate_hd_enhancer.py --batch ./your_photos --output ./hd_results")
    
    if opencv_gui_works:
        print("\n4️⃣  GUI-BASED WEBCAM (If OpenCV GUI works)")
        print("   python windows_hd_webcam.py")
        print("   python simple_hd_webcam.py")
    
    print("\n5️⃣  FRAME SAVER (Alternative to GUI)")
    print("   python hd_webcam_saver.py")
    
    print("\n" + "=" * 60)
    print("🚀 QUICK START RECOMMENDATIONS:")
    print("=" * 60)
    
    print("\n🎯 For BEST RESULTS (No GUI issues):")
    print("   python ultimate_hd_enhancer.py --image test_images/portrait_test.jpg")
    print("   python ultimate_hd_enhancer.py --webcam --duration 20")
    
    print("\n🔧 If you have OpenCV GUI issues:")
    print("   pip uninstall opencv-python opencv-contrib-python")
    print("   pip install opencv-python==********")
    
    print("\n📁 Output Locations:")
    print("   Single images: ./hd_enhanced/")
    print("   Webcam frames: ./webcam_hd_enhanced/")
    print("   Batch results: ./hd_enhanced/ (or custom --output)")
    
    # Interactive testing
    print(f"\n🧪 INTERACTIVE TESTING:")
    print("=" * 60)
    
    response = input("Run quick image test? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        test_img = test_images[0]
        print(f"\n🎯 Testing with {test_img.name}...")
        success = run_command(
            f"python ultimate_hd_enhancer.py --image {test_img}",
            f"Enhancing {test_img.name}"
        )
        
        if success:
            print("✅ Image enhancement test completed!")
            print("📁 Check ./hd_enhanced/ folder for results")
        else:
            print("❌ Image enhancement test failed")
    
    response = input("Run webcam test? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        print("\n🎥 Testing webcam enhancement (10 seconds)...")
        success = run_command(
            "python ultimate_hd_enhancer.py --webcam --duration 10 --interval 2",
            "Webcam HD enhancement test"
        )
        
        if success:
            print("✅ Webcam test completed!")
            print("📁 Check ./webcam_hd_enhanced/ folder for HD frames")
        else:
            print("❌ Webcam test failed")
    
    print(f"\n🎉 HD Enhancement Test Suite Completed!")
    print(f"📖 Use the commands above to enhance your images and webcam to HD quality")

if __name__ == "__main__":
    main()
