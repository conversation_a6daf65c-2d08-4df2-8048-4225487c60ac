#!/usr/bin/env python3
"""
Create Test Images for Enhancement Testing

This script creates various test images to evaluate your enhancement models:
1. Synthetic low-resolution images
2. Noisy images
3. Blurred images
4. Text images
5. Pattern images

Usage:
    python create_test_images.py                # Create all test images
    python create_test_images.py --type text    # Create only text images
    python create_test_images.py --count 10     # Create 10 of each type
"""

import argparse
import cv2
import numpy as np
from pathlib import Path
import random
import string

def create_test_directory():
    """Create test images directory."""
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    return test_dir

def create_synthetic_image(width=320, height=240, complexity='medium'):
    """Create synthetic image with various patterns."""
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    if complexity == 'simple':
        # Simple gradient
        for i in range(height):
            for j in range(width):
                image[i, j] = [i * 255 // height, j * 255 // width, (i + j) * 255 // (height + width)]
    
    elif complexity == 'medium':
        # Geometric shapes
        # Background gradient
        for i in range(height):
            image[i, :] = [50 + i * 100 // height, 100, 150 - i * 100 // height]
        
        # Add circles
        cv2.circle(image, (width//4, height//4), 30, (255, 0, 0), -1)
        cv2.circle(image, (3*width//4, height//4), 25, (0, 255, 0), -1)
        cv2.circle(image, (width//2, 3*height//4), 35, (0, 0, 255), -1)
        
        # Add rectangles
        cv2.rectangle(image, (width//6, height//2), (width//3, 2*height//3), (255, 255, 0), -1)
        cv2.rectangle(image, (2*width//3, height//2), (5*width//6, 2*height//3), (255, 0, 255), -1)
    
    elif complexity == 'complex':
        # Complex patterns
        # Checkerboard background
        for i in range(0, height, 20):
            for j in range(0, width, 20):
                if (i//20 + j//20) % 2 == 0:
                    image[i:i+20, j:j+20] = [100, 100, 100]
                else:
                    image[i:i+20, j:j+20] = [200, 200, 200]
        
        # Add various shapes
        for _ in range(10):
            x, y = random.randint(0, width), random.randint(0, height)
            r = random.randint(10, 30)
            color = [random.randint(0, 255) for _ in range(3)]
            cv2.circle(image, (x, y), r, color, -1)
    
    return image

def create_text_image(width=640, height=480, text_size='medium'):
    """Create image with text for OCR testing."""
    image = np.ones((height, width, 3), dtype=np.uint8) * 255  # White background
    
    # Font settings
    font = cv2.FONT_HERSHEY_SIMPLEX
    if text_size == 'small':
        font_scale = 0.5
        thickness = 1
    elif text_size == 'medium':
        font_scale = 1.0
        thickness = 2
    else:  # large
        font_scale = 1.5
        thickness = 3
    
    # Sample texts
    texts = [
        "AI Image Enhancement",
        "Real-time Processing",
        "High Quality Results",
        "OpenCV + PyTorch",
        "Student-Teacher Model",
        "Knowledge Distillation"
    ]
    
    # Add texts at different positions
    y_start = 50
    for i, text in enumerate(texts):
        y_pos = y_start + i * 60
        if y_pos < height - 20:
            cv2.putText(image, text, (20, y_pos), font, font_scale, (0, 0, 0), thickness)
    
    # Add some colored text
    cv2.putText(image, "Color Text Test", (20, height - 100), font, font_scale, (255, 0, 0), thickness)
    cv2.putText(image, "Multiple Colors", (20, height - 50), font, font_scale, (0, 255, 0), thickness)
    
    return image

def create_noisy_image(base_image, noise_level='medium'):
    """Add noise to an image."""
    if noise_level == 'low':
        noise_std = 10
    elif noise_level == 'medium':
        noise_std = 25
    else:  # high
        noise_std = 50
    
    noise = np.random.normal(0, noise_std, base_image.shape).astype(np.float32)
    noisy_image = base_image.astype(np.float32) + noise
    noisy_image = np.clip(noisy_image, 0, 255).astype(np.uint8)
    
    return noisy_image

def create_blurred_image(base_image, blur_level='medium'):
    """Create blurred version of image."""
    if blur_level == 'low':
        kernel_size = 3
    elif blur_level == 'medium':
        kernel_size = 7
    else:  # high
        kernel_size = 15
    
    blurred = cv2.GaussianBlur(base_image, (kernel_size, kernel_size), 0)
    return blurred

def create_pattern_image(width=320, height=240, pattern_type='stripes'):
    """Create pattern images for testing."""
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    if pattern_type == 'stripes':
        # Vertical stripes
        stripe_width = 20
        for x in range(0, width, stripe_width * 2):
            image[:, x:x+stripe_width] = [255, 255, 255]
    
    elif pattern_type == 'checkerboard':
        # Checkerboard pattern
        square_size = 30
        for i in range(0, height, square_size):
            for j in range(0, width, square_size):
                if (i//square_size + j//square_size) % 2 == 0:
                    image[i:i+square_size, j:j+square_size] = [255, 255, 255]
    
    elif pattern_type == 'circles':
        # Concentric circles
        center_x, center_y = width // 2, height // 2
        for radius in range(10, min(width, height) // 2, 20):
            color = 255 if (radius // 20) % 2 == 0 else 0
            cv2.circle(image, (center_x, center_y), radius, (color, color, color), 2)
    
    elif pattern_type == 'grid':
        # Grid pattern
        image.fill(255)  # White background
        grid_size = 25
        for i in range(0, height, grid_size):
            cv2.line(image, (0, i), (width, i), (0, 0, 0), 1)
        for j in range(0, width, grid_size):
            cv2.line(image, (j, 0), (j, height), (0, 0, 0), 1)
    
    return image

def create_all_test_images(output_dir, count=5):
    """Create all types of test images."""
    print(f"📁 Creating test images in {output_dir}")
    
    # Create synthetic images
    print("🎨 Creating synthetic images...")
    for i in range(count):
        for complexity in ['simple', 'medium', 'complex']:
            img = create_synthetic_image(320, 240, complexity)
            filename = f"synthetic_{complexity}_{i:03d}.png"
            cv2.imwrite(str(output_dir / filename), img)
    
    # Create text images
    print("📝 Creating text images...")
    for i in range(count):
        for size in ['small', 'medium', 'large']:
            img = create_text_image(640, 480, size)
            filename = f"text_{size}_{i:03d}.png"
            cv2.imwrite(str(output_dir / filename), img)
    
    # Create pattern images
    print("🔲 Creating pattern images...")
    for i in range(count):
        for pattern in ['stripes', 'checkerboard', 'circles', 'grid']:
            img = create_pattern_image(320, 240, pattern)
            filename = f"pattern_{pattern}_{i:03d}.png"
            cv2.imwrite(str(output_dir / filename), img)
    
    # Create noisy and blurred versions
    print("🌫️  Creating noisy and blurred images...")
    base_img = create_synthetic_image(320, 240, 'medium')
    
    for i in range(count):
        # Noisy images
        for noise_level in ['low', 'medium', 'high']:
            noisy_img = create_noisy_image(base_img, noise_level)
            filename = f"noisy_{noise_level}_{i:03d}.png"
            cv2.imwrite(str(output_dir / filename), noisy_img)
        
        # Blurred images
        for blur_level in ['low', 'medium', 'high']:
            blurred_img = create_blurred_image(base_img, blur_level)
            filename = f"blurred_{blur_level}_{i:03d}.png"
            cv2.imwrite(str(output_dir / filename), blurred_img)
    
    print(f"✅ Created test images in {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Create test images for enhancement testing")
    parser.add_argument("--type", choices=['all', 'synthetic', 'text', 'pattern', 'noisy', 'blurred'],
                       default='all', help="Type of images to create")
    parser.add_argument("--count", type=int, default=3, help="Number of each type to create")
    parser.add_argument("--output", default="test_images", help="Output directory")
    
    args = parser.parse_args()
    
    print("🎯 Test Image Creator")
    print("=" * 30)
    
    # Create output directory
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    if args.type == 'all':
        create_all_test_images(output_dir, args.count)
    else:
        print(f"🎨 Creating {args.type} images...")
        
        for i in range(args.count):
            if args.type == 'synthetic':
                img = create_synthetic_image(320, 240, 'medium')
                filename = f"synthetic_{i:03d}.png"
            elif args.type == 'text':
                img = create_text_image(640, 480, 'medium')
                filename = f"text_{i:03d}.png"
            elif args.type == 'pattern':
                img = create_pattern_image(320, 240, 'checkerboard')
                filename = f"pattern_{i:03d}.png"
            elif args.type == 'noisy':
                base_img = create_synthetic_image(320, 240, 'medium')
                img = create_noisy_image(base_img, 'medium')
                filename = f"noisy_{i:03d}.png"
            elif args.type == 'blurred':
                base_img = create_synthetic_image(320, 240, 'medium')
                img = create_blurred_image(base_img, 'medium')
                filename = f"blurred_{i:03d}.png"
            
            cv2.imwrite(str(output_dir / filename), img)
        
        print(f"✅ Created {args.count} {args.type} images in {output_dir}")
    
    # Count total images created
    image_files = list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpg"))
    print(f"📊 Total images in directory: {len(image_files)}")

if __name__ == "__main__":
    main()
