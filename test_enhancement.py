#!/usr/bin/env python3
"""
Comprehensive Image Enhancement Testing Tool

This script provides various ways to test your image enhancement models:
1. Single image enhancement
2. Batch image processing
3. Webcam real-time enhancement
4. Performance benchmarking
5. Quality comparison

Usage:
    python test_enhancement.py --help                    # Show all options
    python test_enhancement.py image photo.jpg          # Enhance single image
    python test_enhancement.py batch images/            # Enhance all images in folder
    python test_enhancement.py webcam                   # Real-time webcam
    python test_enhancement.py benchmark               # Performance test
"""

import sys
import argparse
import cv2
import torch
import numpy as np
import time
from pathlib import Path
import glob
from collections import defaultdict
import json

# Add src to path
sys.path.append('src')

def test_single_image(image_path, model_type='mobilenetv2', scale_factor=2, output_dir='test_results'):
    """Test enhancement on a single image."""
    print(f"🖼️  Testing single image: {image_path}")
    
    try:
        from src.models.student_models import StudentModelFactory
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return False
        
        print(f"📏 Original size: {image.shape[1]}x{image.shape[0]}")
        
        # Load model
        print(f"🤖 Loading {model_type} model...")
        model = StudentModelFactory.create_student(model_type, scale_factor=scale_factor)
        model.eval()
        
        # Preprocess
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        
        # Enhance
        print("🚀 Enhancing...")
        start_time = time.time()
        
        with torch.no_grad():
            enhanced_tensor = model(tensor)
        
        processing_time = time.time() - start_time
        
        # Postprocess
        enhanced_np = enhanced_tensor.squeeze(0).permute(1, 2, 0).numpy()
        enhanced_np = np.clip(enhanced_np * 255, 0, 255).astype(np.uint8)
        enhanced_bgr = cv2.cvtColor(enhanced_np, cv2.COLOR_RGB2BGR)
        
        print(f"📐 Enhanced size: {enhanced_bgr.shape[1]}x{enhanced_bgr.shape[0]}")
        print(f"⏱️  Processing time: {processing_time*1000:.1f}ms")
        
        # Save results
        input_name = Path(image_path).stem
        enhanced_path = output_path / f"{input_name}_enhanced_{model_type}_{scale_factor}x.jpg"
        cv2.imwrite(str(enhanced_path), enhanced_bgr)
        
        # Create comparison
        comparison_path = create_comparison(image, enhanced_bgr, output_path / f"{input_name}_comparison_{model_type}_{scale_factor}x.jpg")
        
        print(f"✅ Results saved:")
        print(f"   Enhanced: {enhanced_path}")
        print(f"   Comparison: {comparison_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_batch_images(input_dir, model_type='mobilenetv2', scale_factor=2, output_dir='batch_results'):
    """Test enhancement on multiple images."""
    print(f"📁 Testing batch processing: {input_dir}")
    
    # Find all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_files = []
    
    input_path = Path(input_dir)
    for ext in image_extensions:
        image_files.extend(input_path.glob(ext))
        image_files.extend(input_path.glob(ext.upper()))
    
    if not image_files:
        print(f"❌ No image files found in {input_dir}")
        return False
    
    print(f"📊 Found {len(image_files)} images")
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Process each image
    results = []
    total_time = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] Processing {image_file.name}...")
        
        start_time = time.time()
        success = test_single_image(
            str(image_file), 
            model_type=model_type, 
            scale_factor=scale_factor, 
            output_dir=str(output_path)
        )
        process_time = time.time() - start_time
        total_time += process_time
        
        results.append({
            'file': image_file.name,
            'success': success,
            'time': process_time
        })
    
    # Print summary
    successful = sum(1 for r in results if r['success'])
    print(f"\n📊 Batch Processing Summary:")
    print(f"   Total images: {len(image_files)}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {len(image_files) - successful}")
    print(f"   Total time: {total_time:.1f}s")
    print(f"   Average time per image: {total_time/len(image_files):.1f}s")
    
    return True

def benchmark_models(test_image_path='test_image.jpg', iterations=10):
    """Benchmark different models and settings."""
    print("🏁 Running performance benchmark...")
    
    # Test configurations
    configs = [
        ('tiny', 2),
        ('mobilenetv2', 2),
        ('efficient', 2),
        ('tiny', 4),
        ('mobilenetv2', 4),
        ('efficient', 4),
    ]
    
    # Create test image if not provided
    if not Path(test_image_path).exists():
        print("📷 Creating test image...")
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        cv2.imwrite(test_image_path, test_image)
    
    # Load test image
    test_image = cv2.imread(test_image_path)
    if test_image is None:
        print(f"❌ Could not load test image: {test_image_path}")
        return False
    
    print(f"📏 Test image size: {test_image.shape[1]}x{test_image.shape[0]}")
    print(f"🔄 Running {iterations} iterations per configuration...")
    
    results = {}
    
    try:
        from src.models.student_models import StudentModelFactory
        
        for model_type, scale_factor in configs:
            print(f"\n🧪 Testing {model_type} (scale: {scale_factor}x)...")
            
            # Load model
            model = StudentModelFactory.create_student(model_type, scale_factor=scale_factor)
            model.eval()
            
            # Preprocess
            image_rgb = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
            tensor = torch.from_numpy(image_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
            
            # Warm up
            with torch.no_grad():
                _ = model(tensor)
            
            # Benchmark
            times = []
            for i in range(iterations):
                start_time = time.time()
                with torch.no_grad():
                    enhanced = model(tensor)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # Convert to ms
            
            # Calculate statistics
            avg_time = np.mean(times)
            std_time = np.std(times)
            min_time = np.min(times)
            max_time = np.max(times)
            fps = 1000 / avg_time
            
            results[f"{model_type}_{scale_factor}x"] = {
                'avg_time_ms': avg_time,
                'std_time_ms': std_time,
                'min_time_ms': min_time,
                'max_time_ms': max_time,
                'fps': fps,
                'output_size': enhanced.shape[2:4]  # H, W
            }
            
            print(f"   Average: {avg_time:.1f}ms ({fps:.1f} FPS)")
            print(f"   Range: {min_time:.1f}-{max_time:.1f}ms")
            print(f"   Output size: {enhanced.shape[3]}x{enhanced.shape[2]}")
    
    except Exception as e:
        print(f"❌ Benchmark error: {e}")
        return False
    
    # Print summary table
    print(f"\n📊 Benchmark Results Summary:")
    print("=" * 80)
    print(f"{'Model':<20} {'Scale':<6} {'Avg Time':<12} {'FPS':<8} {'Output Size':<12}")
    print("-" * 80)
    
    for config_name, stats in results.items():
        model_name, scale = config_name.split('_')
        output_size = f"{stats['output_size'][1]}x{stats['output_size'][0]}"
        print(f"{model_name:<20} {scale:<6} {stats['avg_time_ms']:<12.1f} {stats['fps']:<8.1f} {output_size:<12}")
    
    # Save results
    with open('benchmark_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Detailed results saved to benchmark_results.json")
    
    return True

def create_comparison(original, enhanced, output_path):
    """Create side-by-side comparison image."""
    try:
        # Resize original to match enhanced height
        h_enhanced = enhanced.shape[0]
        h_original = original.shape[0]
        w_original = original.shape[1]
        
        new_w = int(w_original * h_enhanced / h_original)
        original_resized = cv2.resize(original, (new_w, h_enhanced))
        
        # Create comparison
        comparison = np.hstack([original_resized, enhanced])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 255, 0), 2)
        cv2.putText(comparison, "Enhanced", (new_w + 10, 30), font, 1, (0, 255, 0), 2)
        
        cv2.imwrite(str(output_path), comparison)
        return output_path
        
    except Exception as e:
        print(f"❌ Error creating comparison: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Comprehensive image enhancement testing")
    subparsers = parser.add_subparsers(dest='command', help='Test commands')
    
    # Single image command
    img_parser = subparsers.add_parser('image', help='Test single image')
    img_parser.add_argument('path', help='Path to image file')
    img_parser.add_argument('--model', '-m', choices=['mobilenetv2', 'tiny', 'efficient'], 
                           default='mobilenetv2', help='Model type')
    img_parser.add_argument('--scale', '-s', type=int, choices=[2, 4], default=2, help='Scale factor')
    img_parser.add_argument('--output', '-o', default='test_results', help='Output directory')
    
    # Batch processing command
    batch_parser = subparsers.add_parser('batch', help='Test batch of images')
    batch_parser.add_argument('directory', help='Directory containing images')
    batch_parser.add_argument('--model', '-m', choices=['mobilenetv2', 'tiny', 'efficient'], 
                             default='mobilenetv2', help='Model type')
    batch_parser.add_argument('--scale', '-s', type=int, choices=[2, 4], default=2, help='Scale factor')
    batch_parser.add_argument('--output', '-o', default='batch_results', help='Output directory')
    
    # Webcam command
    webcam_parser = subparsers.add_parser('webcam', help='Real-time webcam enhancement')
    webcam_parser.add_argument('--model', '-m', choices=['mobilenetv2', 'tiny', 'efficient'], 
                              default='mobilenetv2', help='Model type')
    webcam_parser.add_argument('--scale', '-s', type=int, choices=[2, 4], default=2, help='Scale factor')
    
    # Benchmark command
    bench_parser = subparsers.add_parser('benchmark', help='Performance benchmark')
    bench_parser.add_argument('--image', help='Test image path (optional)')
    bench_parser.add_argument('--iterations', '-i', type=int, default=10, help='Number of iterations')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🎯 AI Image Enhancement Testing")
    print("=" * 50)
    
    if args.command == 'image':
        test_single_image(args.path, args.model, args.scale, args.output)
    
    elif args.command == 'batch':
        test_batch_images(args.directory, args.model, args.scale, args.output)
    
    elif args.command == 'webcam':
        print("🎥 Starting webcam enhancement...")
        print("Note: This will import and run the webcam enhancer")
        try:
            from webcam_enhance import WebcamEnhancer
            enhancer = WebcamEnhancer(
                model_type=args.model,
                scale_factor=args.scale,
                target_fps=30,
                save_frames=False
            )
            enhancer.run()
        except ImportError:
            print("❌ Could not import webcam enhancer. Make sure webcam_enhance.py exists.")
    
    elif args.command == 'benchmark':
        test_image = args.image if args.image else 'test_image.jpg'
        benchmark_models(test_image, args.iterations)

if __name__ == "__main__":
    main()
