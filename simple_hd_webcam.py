#!/usr/bin/env python3
"""
Simplified HD Webcam Enhancement

A more reliable version focused on getting the webcam window to show properly.
"""

import cv2
import numpy as np
import time
import argparse

class SimpleHDEnhancer:
    """Simplified HD enhancer that focuses on reliability."""
    
    def __init__(self):
        print("🎯 Simple HD Enhancer initialized")
    
    def enhance_frame(self, frame):
        """Simple but effective enhancement."""
        # Convert to float for processing
        img_float = frame.astype(np.float32)
        
        # 1. Noise reduction
        denoised = cv2.bilateralFilter(frame, 9, 75, 75)
        
        # 2. Sharpening
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 3. Contrast enhancement
        lab = cv2.cvtColor(sharpened, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # 4. Resize to HD if needed
        height, width = enhanced.shape[:2]
        if height < 1080:
            scale = 1080 / height
            new_width = int(width * scale)
            enhanced = cv2.resize(enhanced, (new_width, 1080), interpolation=cv2.INTER_LANCZOS4)
        
        return enhanced
    
    def add_info_overlay(self, frame, fps, processing_time, enhanced_size):
        """Add information overlay."""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        color = (0, 255, 0)
        thickness = 2
        
        # Semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (400, 120), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Text information
        texts = [
            f"HD Enhancement: ACTIVE",
            f"FPS: {fps:.1f}",
            f"Processing: {processing_time:.1f}ms",
            f"Resolution: {enhanced_size[1]}x{enhanced_size[0]}"
        ]
        
        y_offset = 30
        for text in texts:
            cv2.putText(frame, text, (15, y_offset), font, font_scale, color, thickness)
            y_offset += 20
    
    def run_webcam(self):
        """Run the HD webcam enhancement."""
        print("🎥 Starting Simple HD Webcam...")
        print("Controls:")
        print("  'q' or ESC - Quit")
        print("  's' - Save HD frame")
        print("  'i' - Toggle info overlay")
        print("  'e' - Toggle enhancement")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot open webcam at index 0")
            print("🔧 Trying other webcam indices...")
            for i in range(1, 5):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    print(f"✅ Found webcam at index {i}")
                    break
            else:
                print("❌ No webcam found")
                return False
        
        # Set webcam properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Get actual resolution
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        print(f"📹 Webcam resolution: {width}x{height}")
        
        # Create window with error handling
        window_name = 'Simple_HD_Webcam'  # No spaces for Windows compatibility
        try:
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_name, 1280, 720)
            print(f"🖼️  Created window: '{window_name}'")
        except Exception as e:
            print(f"⚠️  Window creation warning: {e}")
            print("🔧 Will try to create window during first frame display")
        
        # State variables
        show_info = True
        enhancement_enabled = True
        frame_count = 0
        fps_history = []
        last_time = time.time()
        
        print("🎬 Starting webcam loop...")
        print("📺 Window should appear now!")
        
        try:
            while True:
                # Capture frame
                ret, frame = cap.read()
                if not ret:
                    print("❌ Failed to read frame")
                    time.sleep(0.1)
                    continue
                
                current_time = time.time()
                
                # Process frame
                start_process = time.time()
                if enhancement_enabled:
                    display_frame = self.enhance_frame(frame)
                else:
                    display_frame = frame.copy()
                
                processing_time = (time.time() - start_process) * 1000
                
                # Calculate FPS
                fps = 1.0 / (current_time - last_time) if current_time > last_time else 0
                fps_history.append(fps)
                if len(fps_history) > 30:
                    fps_history.pop(0)
                
                avg_fps = sum(fps_history) / len(fps_history) if fps_history else 0
                last_time = current_time
                
                # Add info overlay
                if show_info:
                    self.add_info_overlay(display_frame, avg_fps, processing_time, display_frame.shape)
                
                # Show frame with error handling
                try:
                    cv2.imshow(window_name, display_frame)
                except Exception as e:
                    print(f"⚠️  Display error: {e}")
                    # Try to recreate window
                    try:
                        cv2.destroyWindow(window_name)
                        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                        cv2.imshow(window_name, display_frame)
                        print("🔧 Window recreated successfully")
                    except Exception as e2:
                        print(f"❌ Cannot display frame: {e2}")
                        break
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q') or key == 27:  # 'q' or ESC
                    print("🛑 Quit requested")
                    break
                elif key == ord('s'):
                    timestamp = int(time.time())
                    filename = f"hd_frame_{timestamp}.jpg"
                    cv2.imwrite(filename, display_frame, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    print(f"💾 Saved: {filename} ({display_frame.shape[1]}x{display_frame.shape[0]})")
                    frame_count += 1
                elif key == ord('i'):
                    show_info = not show_info
                    print(f"ℹ️  Info overlay: {'ON' if show_info else 'OFF'}")
                elif key == ord('e'):
                    enhancement_enabled = not enhancement_enabled
                    print(f"🎯 Enhancement: {'ON' if enhancement_enabled else 'OFF'}")
                
                # Check if window was closed
                try:
                    if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
                        print("🖼️  Window was closed")
                        break
                except:
                    # Window might not exist yet
                    pass
        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        finally:
            # Cleanup
            print("🧹 Cleaning up...")
            cap.release()
            cv2.destroyAllWindows()
            
            # Statistics
            if fps_history:
                avg_fps = sum(fps_history) / len(fps_history)
                print(f"📊 Average FPS: {avg_fps:.1f}")
            
            print(f"💾 Saved {frame_count} frames")
            print("✅ Webcam session ended")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Simple HD Webcam Enhancement")
    parser.add_argument("--test", action="store_true", help="Run basic webcam test first")
    
    args = parser.parse_args()
    
    print("🎯 Simple HD Webcam Enhancement")
    print("=" * 40)
    
    if args.test:
        print("🧪 Running basic webcam test...")
        
        # Basic test
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f"✅ Basic test passed: {frame.shape}")
                cv2.imshow('Basic Test', frame)
                cv2.waitKey(2000)  # Show for 2 seconds
                cv2.destroyAllWindows()
            else:
                print("❌ Cannot read frames")
                return
            cap.release()
        else:
            print("❌ Cannot open webcam")
            return
    
    # Run HD enhancement
    enhancer = SimpleHDEnhancer()
    enhancer.run_webcam()

if __name__ == "__main__":
    main()
