#!/usr/bin/env python3
"""
HD Enhancement Setup and Run Script

Automatically sets up and runs the HD enhancement system.
Handles dependencies, testing, and provides easy commands.

Usage:
    python setup_and_run_hd.py --setup          # Install dependencies
    python setup_and_run_hd.py --test           # Run all tests
    python setup_and_run_hd.py --webcam         # Start HD webcam
    python setup_and_run_hd.py --enhance image.jpg  # Enhance image
    python setup_and_run_hd.py --demo           # Full demo
"""

import sys
import subprocess
import argparse
from pathlib import Path
import time
import os

def run_command(command, description="", check=True):
    """Run a command with proper error handling."""
    print(f"🔧 {description}")
    print(f"   Command: {command}")
    
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(command, check=check, 
                                  capture_output=True, text=True)
        
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        
        if result.returncode == 0:
            print(f"   ✅ Success")
        else:
            print(f"   ❌ Failed (exit code: {result.returncode})")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
        
        return result.returncode == 0
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Command failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    print(f"   Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Python 3.8+ required")
        return False
    
    print("   ✅ Python version compatible")
    return True

def setup_dependencies():
    """Install required dependencies."""
    print("📦 Setting up HD Enhancement dependencies...")
    
    # Check if pip is available
    if not run_command([sys.executable, "-m", "pip", "--version"], "Checking pip", check=False):
        print("❌ pip not available")
        return False
    
    # Upgrade pip
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                "Upgrading pip")
    
    # Install core dependencies first
    core_deps = [
        "opencv-python>=4.8.0",
        "numpy>=1.24.0",
        "Pillow>=10.0.0"
    ]
    
    print("🔧 Installing core dependencies...")
    for dep in core_deps:
        success = run_command([sys.executable, "-m", "pip", "install", dep], 
                            f"Installing {dep}")
        if not success:
            print(f"❌ Failed to install {dep}")
            return False
    
    # Install from requirements file if it exists
    req_file = Path("requirements_hd.txt")
    if req_file.exists():
        print("📋 Installing from requirements_hd.txt...")
        success = run_command([sys.executable, "-m", "pip", "install", "-r", str(req_file)], 
                            "Installing HD requirements")
        if not success:
            print("⚠️  Some optional dependencies may not be installed")
    
    # Test imports
    print("🧪 Testing imports...")
    test_imports = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow")
    ]
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"   ✅ {name} imported successfully")
        except ImportError as e:
            print(f"   ❌ {name} import failed: {e}")
            return False
    
    print("✅ Dependencies setup completed!")
    return True

def create_test_image():
    """Create a test image for demonstration."""
    print("🎨 Creating test image...")
    
    try:
        import cv2
        import numpy as np
        
        # Create a test image with fine details
        img = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Background gradient
        for i in range(480):
            for j in range(640):
                img[i, j] = [100 + i//3, 150 + j//4, 200 - i//4]
        
        # Add text
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(img, "HD ENHANCEMENT TEST", (50, 100), font, 1.5, (255, 255, 255), 3)
        cv2.putText(img, "Original: 640x480", (50, 150), font, 1, (255, 255, 255), 2)
        cv2.putText(img, "Target: 1080p HD", (50, 200), font, 1, (255, 255, 255), 2)
        
        # Add fine details
        for i in range(0, 480, 20):
            cv2.line(img, (0, i), (640, i), (255, 255, 255), 1)
        for j in range(0, 640, 20):
            cv2.line(img, (j, 0), (j, 480), (255, 255, 255), 1)
        
        # Add circles for detail test
        for x in range(100, 600, 100):
            cv2.circle(img, (x, 300), 30, (0, 255, 255), 2)
            cv2.circle(img, (x, 300), 15, (255, 0, 255), -1)
        
        test_path = "hd_test_image.jpg"
        cv2.imwrite(test_path, img, [cv2.IMWRITE_JPEG_QUALITY, 100])
        print(f"   ✅ Test image created: {test_path}")
        return test_path
        
    except Exception as e:
        print(f"   ❌ Failed to create test image: {e}")
        return None

def run_tests():
    """Run comprehensive tests."""
    print("🧪 Running HD Enhancement tests...")
    
    # Create test samples
    success = run_command([sys.executable, "test_hd_enhancement.py", "--create-samples"], 
                         "Creating test samples")
    if not success:
        print("❌ Failed to create test samples")
        return False
    
    # Run benchmark
    success = run_command([sys.executable, "test_hd_enhancement.py", "--benchmark"], 
                         "Running performance benchmark")
    if not success:
        print("❌ Benchmark failed")
        return False
    
    # Test with sample image
    test_image = create_test_image()
    if test_image:
        success = run_command([sys.executable, "test_hd_enhancement.py", "--test-image", test_image], 
                             f"Testing image enhancement on {test_image}")
        if not success:
            print("❌ Image test failed")
            return False
    
    print("✅ All tests completed successfully!")
    return True

def enhance_image(image_path):
    """Enhance a single image."""
    print(f"🎯 Enhancing image: {image_path}")
    
    if not Path(image_path).exists():
        print(f"❌ Image not found: {image_path}")
        return False
    
    success = run_command([sys.executable, "hd_enhance.py", image_path], 
                         f"HD enhancing {image_path}")
    
    if success:
        print("✅ Image enhancement completed!")
        # Look for output file
        input_path = Path(image_path)
        output_path = input_path.parent / f"{input_path.stem}_hd_enhanced{input_path.suffix}"
        if output_path.exists():
            print(f"📁 Enhanced image saved: {output_path}")
    
    return success

def start_webcam():
    """Start HD webcam enhancement."""
    print("🎥 Starting HD webcam enhancement...")
    print("Controls:")
    print("  - 'q': Quit")
    print("  - 's': Save HD frame")
    print("  - 'i': Toggle info overlay")
    
    success = run_command([sys.executable, "hd_enhance.py", "--webcam"], 
                         "Starting HD webcam")
    return success

def run_demo():
    """Run full demonstration."""
    print("🎬 Running HD Enhancement Demo...")
    print("=" * 60)
    
    # Step 1: Setup
    print("\n1️⃣  Setting up dependencies...")
    if not setup_dependencies():
        print("❌ Setup failed")
        return False
    
    # Step 2: Create and test image
    print("\n2️⃣  Testing image enhancement...")
    test_image = create_test_image()
    if test_image:
        enhance_image(test_image)
    
    # Step 3: Run benchmark
    print("\n3️⃣  Running performance benchmark...")
    run_command([sys.executable, "test_hd_enhancement.py", "--benchmark"], 
                "Performance benchmark")
    
    # Step 4: Webcam demo
    print("\n4️⃣  HD Webcam demo (optional)...")
    response = input("Start HD webcam demo? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        start_webcam()
    
    print("\n🎉 HD Enhancement demo completed!")
    print("\nNext steps:")
    print("  - Use: python hd_enhance.py your_image.jpg")
    print("  - Webcam: python hd_enhance.py --webcam")
    print("  - Test: python test_hd_enhancement.py --all")
    
    return True

def print_usage_examples():
    """Print usage examples."""
    print("\n📖 HD Enhancement Usage Examples:")
    print("=" * 50)
    print()
    print("🖼️  Single Image Enhancement:")
    print("   python hd_enhance.py photo.jpg")
    print("   python hd_enhance.py photo.jpg --output hd_photo.jpg")
    print()
    print("🎥 HD Webcam Enhancement:")
    print("   python hd_enhance.py --webcam")
    print()
    print("🧪 Testing and Benchmarks:")
    print("   python test_hd_enhancement.py --create-samples")
    print("   python test_hd_enhancement.py --benchmark")
    print("   python test_hd_enhancement.py --test-image photo.jpg")
    print("   python test_hd_enhancement.py --all")
    print()
    print("🔧 Setup and Maintenance:")
    print("   python setup_and_run_hd.py --setup")
    print("   python setup_and_run_hd.py --test")
    print("   python setup_and_run_hd.py --demo")
    print()

def main():
    parser = argparse.ArgumentParser(
        description="HD Enhancement Setup and Run Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python setup_and_run_hd.py --setup              # Install dependencies
  python setup_and_run_hd.py --test               # Run all tests
  python setup_and_run_hd.py --enhance photo.jpg  # Enhance image
  python setup_and_run_hd.py --webcam             # HD webcam
  python setup_and_run_hd.py --demo               # Full demo
        """
    )
    
    parser.add_argument("--setup", action="store_true", help="Install dependencies")
    parser.add_argument("--test", action="store_true", help="Run comprehensive tests")
    parser.add_argument("--enhance", help="Enhance single image")
    parser.add_argument("--webcam", action="store_true", help="Start HD webcam")
    parser.add_argument("--demo", action="store_true", help="Run full demonstration")
    parser.add_argument("--examples", action="store_true", help="Show usage examples")
    
    args = parser.parse_args()
    
    if not any([args.setup, args.test, args.enhance, args.webcam, args.demo, args.examples]):
        parser.print_help()
        print_usage_examples()
        return
    
    print("🎯 HD Enhancement System")
    print("=" * 50)
    print("True 1080p HD quality enhancement")
    print("No artistic effects - genuine quality improvement")
    print("=" * 50)
    
    # Check Python version first
    if not check_python_version():
        return
    
    success = True
    
    if args.setup:
        success = setup_dependencies()
    
    if args.test and success:
        success = run_tests()
    
    if args.enhance and success:
        success = enhance_image(args.enhance)
    
    if args.webcam and success:
        success = start_webcam()
    
    if args.demo:
        success = run_demo()
    
    if args.examples:
        print_usage_examples()
    
    if success:
        print("\n✅ HD Enhancement system ready!")
    else:
        print("\n❌ Some operations failed. Check the output above.")

if __name__ == "__main__":
    main()
