#!/usr/bin/env python3
"""
HD Webcam Frame Saver

If OpenCV window display fails, this saves HD-enhanced frames to disk
so you can see the HD enhancement results.
"""

import cv2
import numpy as np
import time
import os
from pathlib import Path

class HDFrameSaver:
    """Save HD-enhanced webcam frames to disk."""
    
    def __init__(self):
        self.output_dir = Path("hd_webcam_frames")
        self.output_dir.mkdir(exist_ok=True)
        print(f"📁 Output directory: {self.output_dir}")
    
    def enhance_to_hd(self, frame):
        """Enhance frame to 1080p HD quality."""
        # Step 1: Noise reduction
        denoised = cv2.bilateralFilter(frame, 9, 75, 75)
        
        # Step 2: Unsharp masking
        gaussian = cv2.GaussianBlur(denoised, (0, 0), 2.0)
        unsharp = cv2.addWeighted(denoised, 1.5, gaussian, -0.5, 0)
        
        # Step 3: Contrast enhancement
        lab = cv2.cvtColor(unsharp, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        l_enhanced = clahe.apply(l)
        
        enhanced_lab = cv2.merge([l_enhanced, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        # Step 4: Final sharpening
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]], dtype=np.float32)
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
        
        # Step 5: Scale to HD
        height, width = result.shape[:2]
        if height < 1080:
            scale = 1080 / height
            new_width = int(width * scale)
            result = cv2.resize(result, (new_width, 1080), interpolation=cv2.INTER_LANCZOS4)
        
        return result
    
    def add_comparison_info(self, original, enhanced):
        """Create side-by-side comparison with info."""
        # Resize original to match enhanced height for comparison
        orig_h, orig_w = original.shape[:2]
        enh_h, enh_w = enhanced.shape[:2]
        
        # Scale original to same height as enhanced
        scale = enh_h / orig_h
        new_orig_w = int(orig_w * scale)
        original_scaled = cv2.resize(original, (new_orig_w, enh_h), interpolation=cv2.INTER_LANCZOS4)
        
        # Create comparison
        comparison = np.hstack([original_scaled, enhanced])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.2
        thickness = 3
        
        # Original label
        cv2.putText(comparison, f"ORIGINAL: {orig_w}x{orig_h}", (20, 50), 
                   font, font_scale, (0, 0, 255), thickness)
        
        # Enhanced label
        cv2.putText(comparison, f"HD ENHANCED: {enh_w}x{enh_h}", 
                   (original_scaled.shape[1] + 20, 50), 
                   font, font_scale, (0, 255, 0), thickness)
        
        # Quality info
        cv2.putText(comparison, "TRUE 1080p HD QUALITY", 
                   (comparison.shape[1]//2 - 200, comparison.shape[0] - 30), 
                   font, font_scale, (255, 255, 0), thickness)
        
        return comparison
    
    def run_hd_capture(self, duration=30, save_interval=2):
        """Capture and save HD frames."""
        print(f"🎥 Starting HD frame capture for {duration} seconds...")
        print(f"💾 Saving every {save_interval} seconds")
        print("Press Ctrl+C to stop early")
        
        # Initialize webcam
        cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        if not cap.isOpened():
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                print("❌ Cannot open webcam")
                return False
        
        # Set properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        print(f"📹 Webcam: {actual_width}x{actual_height}")
        
        start_time = time.time()
        last_save_time = start_time
        frame_count = 0
        saved_count = 0
        
        try:
            while time.time() - start_time < duration:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Cannot read frame")
                    continue
                
                frame_count += 1
                current_time = time.time()
                
                # Save frame at intervals
                if current_time - last_save_time >= save_interval:
                    print(f"📸 Processing frame {frame_count}...")
                    
                    # Enhance to HD
                    start_enhance = time.time()
                    hd_frame = self.enhance_to_hd(frame)
                    enhance_time = (time.time() - start_enhance) * 1000
                    
                    # Create comparison
                    comparison = self.add_comparison_info(frame, hd_frame)
                    
                    # Save files
                    timestamp = int(current_time)
                    
                    # Save original
                    orig_path = self.output_dir / f"original_{timestamp}.jpg"
                    cv2.imwrite(str(orig_path), frame, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    # Save HD enhanced
                    hd_path = self.output_dir / f"hd_enhanced_{timestamp}.jpg"
                    cv2.imwrite(str(hd_path), hd_frame, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    # Save comparison
                    comp_path = self.output_dir / f"comparison_{timestamp}.jpg"
                    cv2.imwrite(str(comp_path), comparison, [cv2.IMWRITE_JPEG_QUALITY, 100])
                    
                    saved_count += 1
                    orig_size = frame.shape
                    hd_size = hd_frame.shape
                    
                    print(f"✅ Saved set {saved_count}:")
                    print(f"   Original: {orig_size[1]}x{orig_size[0]} -> {orig_path.name}")
                    print(f"   HD Enhanced: {hd_size[1]}x{hd_size[0]} -> {hd_path.name}")
                    print(f"   Comparison: -> {comp_path.name}")
                    print(f"   Processing time: {enhance_time:.1f}ms")
                    
                    last_save_time = current_time
                
                # Progress indicator
                elapsed = current_time - start_time
                remaining = duration - elapsed
                if frame_count % 30 == 0:
                    print(f"⏱️  {remaining:.1f}s remaining, {frame_count} frames processed")
        
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        
        finally:
            cap.release()
            
            print(f"\n📊 HD Capture Summary:")
            print(f"   Duration: {time.time() - start_time:.1f}s")
            print(f"   Frames processed: {frame_count}")
            print(f"   HD sets saved: {saved_count}")
            print(f"   Output directory: {self.output_dir}")
            
            # List saved files
            saved_files = list(self.output_dir.glob("*.jpg"))
            print(f"   Total files: {len(saved_files)}")
            
            if saved_files:
                print(f"\n📁 Saved files in {self.output_dir}:")
                for file in sorted(saved_files)[-6:]:  # Show last 6 files
                    size = file.stat().st_size / 1024  # KB
                    print(f"   {file.name} ({size:.1f} KB)")
                
                print(f"\n🎯 Open {self.output_dir} to see your HD enhanced images!")
                print("   Look for 'comparison_*.jpg' files to see before/after")
        
        return True

def main():
    print("🎯 HD Webcam Frame Saver")
    print("Saves HD-enhanced webcam frames to disk")
    print("=" * 50)
    
    saver = HDFrameSaver()
    
    print("Options:")
    print("1. Quick test (10 seconds, save every 2 seconds)")
    print("2. Standard capture (30 seconds, save every 3 seconds)")
    print("3. Extended capture (60 seconds, save every 5 seconds)")
    print("4. Custom settings")
    
    choice = input("Choose option (1-4): ").strip()
    
    if choice == "1":
        duration, interval = 10, 2
    elif choice == "2":
        duration, interval = 30, 3
    elif choice == "3":
        duration, interval = 60, 5
    elif choice == "4":
        try:
            duration = int(input("Duration (seconds): "))
            interval = int(input("Save interval (seconds): "))
        except ValueError:
            print("Invalid input, using defaults")
            duration, interval = 30, 3
    else:
        duration, interval = 30, 3
    
    print(f"\n🎬 Starting HD capture: {duration}s duration, save every {interval}s")
    success = saver.run_hd_capture(duration, interval)
    
    if success:
        print("\n🎉 HD capture completed!")
        print(f"📂 Check the '{saver.output_dir}' folder for your HD enhanced images")
    else:
        print("\n❌ HD capture failed")

if __name__ == "__main__":
    main()
