#!/usr/bin/env python3
"""
Launch Web Interface for Image Enhancement

This script launches the Streamlit web interface for easy image enhancement.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_streamlit():
    """Check if Streamlit is installed."""
    try:
        import streamlit
        return True
    except ImportError:
        return False

def install_streamlit():
    """Install Streamlit."""
    print("📦 Installing Streamlit...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        print("✅ Streamlit installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Streamlit")
        return False

def launch_web_interface():
    """Launch the Streamlit web interface."""
    print("🌐 Launching Web Interface...")
    print("=" * 40)
    
    # Check if Streamlit is available
    if not check_streamlit():
        print("⚠️  Streamlit not found. Installing...")
        if not install_streamlit():
            print("❌ Could not install Streamlit. Please install manually:")
            print("   pip install streamlit")
            return
    
    # Check if the UI file exists
    ui_file = Path("src/ui/streamlit_app.py")
    if not ui_file.exists():
        print(f"❌ UI file not found: {ui_file}")
        return
    
    print("🚀 Starting web interface...")
    print("📱 The interface will open in your browser automatically")
    print("🔗 URL: http://localhost:8501")
    print()
    print("Features available:")
    print("  📁 Upload custom images")
    print("  📷 Use webcam capture")
    print("  📊 Batch processing")
    print("  📈 Quality metrics")
    print("  💾 Download enhanced images")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 40)
    
    try:
        # Launch Streamlit
        subprocess.run([sys.executable, "-m", "streamlit", "run", str(ui_file)])
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped")
    except Exception as e:
        print(f"❌ Error launching web interface: {e}")

if __name__ == "__main__":
    launch_web_interface()
