#!/usr/bin/env python3
"""
Test Advanced Image Enhancement System

Test the advanced enhancement system with anti-halo sharpening,
vibrancy boost, edge-aware filtering, and real-time optimization.

Usage:
    python test_advanced_enhancement.py --test-single photo.jpg
    python test_advanced_enhancement.py --test-batch images/
    python test_advanced_enhancement.py --benchmark-realtime
    python test_advanced_enhancement.py --validate-ssim
"""

import sys
import argparse
from pathlib import Path
import cv2
import numpy as np
import time
import json
from advanced_enhance import AdvancedImageEnhancer, RealTimeVideoEnhancer
from skimage import measure

def test_single_image(image_path, output_dir="advanced_test_results"):
    """Test advanced enhancement on a single image."""
    print(f"🔍 Testing advanced enhancement on: {image_path}")
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    enhancer = AdvancedImageEnhancer()
    
    # Test different configurations
    test_configs = [
        {
            'name': 'standard',
            'anti_halo': True,
            'vibrancy_boost': 1.2,
            'edge_filter': 'bilateral',
            'real_time': False
        },
        {
            'name': 'high_quality',
            'anti_halo': True,
            'vibrancy_boost': 1.4,
            'edge_filter': 'guided',
            'real_time': False
        },
        {
            'name': 'real_time',
            'anti_halo': True,
            'vibrancy_boost': 1.1,
            'edge_filter': 'bilateral',
            'real_time': True
        },
        {
            'name': 'maximum_vibrancy',
            'anti_halo': True,
            'vibrancy_boost': 1.8,
            'edge_filter': 'adaptive',
            'real_time': False
        }
    ]
    
    results = []
    original_image = cv2.imread(image_path)
    
    for config in test_configs:
        print(f"\n   Testing {config['name']} configuration...")
        
        output_file = output_path / f"{Path(image_path).stem}_{config['name']}.jpg"
        
        start_time = time.time()
        success = enhancer.enhance_image(
            input_path=image_path,
            output_path=str(output_file),
            anti_halo=config['anti_halo'],
            vibrancy_boost=config['vibrancy_boost'],
            edge_filter=config['edge_filter'],
            real_time=config['real_time'],
            ssim_target=0.90,
            use_ai_model=not config['real_time']
        )
        processing_time = time.time() - start_time
        
        # Calculate SSIM if successful
        ssim_value = 0.0
        if success and output_file.exists():
            enhanced_image = cv2.imread(str(output_file))
            if enhanced_image is not None:
                # Resize original to match enhanced for SSIM calculation
                original_resized = cv2.resize(original_image, 
                                            (enhanced_image.shape[1], enhanced_image.shape[0]))
                ssim_value = enhancer.calculate_ssim(original_resized, enhanced_image)
        
        results.append({
            'config': config['name'],
            'success': success,
            'processing_time': processing_time,
            'ssim': ssim_value,
            'output_file': str(output_file),
            'settings': config
        })
        
        if success:
            fps_equivalent = 1000 / (processing_time * 1000) if processing_time > 0 else 0
            print(f"      ✅ Success - {processing_time*1000:.1f}ms ({fps_equivalent:.1f} FPS)")
            print(f"      📊 SSIM: {ssim_value:.3f}")
        else:
            print(f"      ❌ Failed")
    
    return results

def test_batch_images(input_dir, output_dir="advanced_batch_results"):
    """Test advanced enhancement on multiple images."""
    print(f"📁 Testing batch advanced enhancement: {input_dir}")
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Find all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(input_path.glob(ext))
        image_files.extend(input_path.glob(ext.upper()))
    
    if not image_files:
        print(f"❌ No image files found in {input_dir}")
        return []
    
    print(f"📊 Found {len(image_files)} images")
    
    enhancer = AdvancedImageEnhancer()
    results = []
    total_time = 0
    total_ssim = 0
    successful = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] Processing {image_file.name}...")
        
        output_file = output_path / f"{image_file.stem}_advanced_enhanced.jpg"
        
        start_time = time.time()
        success = enhancer.enhance_image(
            input_path=str(image_file),
            output_path=str(output_file),
            anti_halo=True,
            vibrancy_boost=1.2,
            edge_filter='bilateral',
            real_time=False,
            ssim_target=0.90,
            use_ai_model=True
        )
        processing_time = time.time() - start_time
        total_time += processing_time
        
        # Calculate SSIM
        ssim_value = 0.0
        if success:
            ssim_value = enhancer.processing_metrics.get('ssim', 0.0)
            total_ssim += ssim_value
            successful += 1
        
        results.append({
            'file': image_file.name,
            'success': success,
            'processing_time': processing_time,
            'ssim': ssim_value
        })
    
    # Print summary
    avg_ssim = total_ssim / successful if successful > 0 else 0
    print(f"\n📊 Batch Processing Summary:")
    print(f"   Total images: {len(image_files)}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {len(image_files) - successful}")
    print(f"   Total time: {total_time:.1f}s")
    print(f"   Average time per image: {total_time/len(image_files):.1f}s")
    print(f"   Average SSIM: {avg_ssim:.3f}")
    print(f"   SSIM > 0.90: {sum(1 for r in results if r['ssim'] > 0.90)}/{successful}")
    
    return results

def benchmark_realtime_performance():
    """Benchmark real-time video enhancement performance."""
    print("🏁 Benchmarking real-time video enhancement...")
    
    # Create test video frames
    test_frames = []
    for i in range(100):  # 100 test frames
        frame = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
        # Add some structure to make it more realistic
        cv2.rectangle(frame, (100, 100), (500, 400), (255, 255, 255), -1)
        cv2.circle(frame, (800, 300), 100, (0, 0, 255), -1)
        test_frames.append(frame)
    
    # Test different configurations
    configs = [
        {'anti_halo': True, 'vibrancy_boost': 1.1, 'edge_filter': 'bilateral'},
        {'anti_halo': True, 'vibrancy_boost': 1.2, 'edge_filter': 'bilateral'},
        {'anti_halo': False, 'vibrancy_boost': 1.1, 'edge_filter': 'bilateral'},
        {'anti_halo': True, 'vibrancy_boost': 1.1, 'edge_filter': 'guided'},
    ]
    
    results = {}
    
    for i, config in enumerate(configs):
        config_name = f"config_{i+1}"
        print(f"\n🧪 Testing {config_name}: {config}")
        
        enhancer = RealTimeVideoEnhancer(**config)
        
        # Process all frames
        start_time = time.time()
        for frame in test_frames:
            enhanced_frame = enhancer.process_frame(frame)
        total_time = time.time() - start_time
        
        # Get performance stats
        stats = enhancer.get_performance_stats()
        
        results[config_name] = {
            'config': config,
            'total_time': total_time,
            'avg_fps': stats['fps'],
            'avg_processing_time_ms': stats['avg_processing_time_ms'],
            'frames_processed': stats['frames_processed']
        }
        
        print(f"   Average FPS: {stats['fps']:.1f}")
        print(f"   Average processing time: {stats['avg_processing_time_ms']:.1f}ms")
        print(f"   Real-time capable (>30 FPS): {'✅' if stats['fps'] > 30 else '❌'}")
    
    # Print summary table
    print(f"\n📊 Real-time Performance Benchmark Results:")
    print("=" * 80)
    print(f"{'Config':<10} {'Anti-Halo':<10} {'Vibrancy':<9} {'Filter':<10} {'FPS':<8} {'RT Ready':<8}")
    print("-" * 80)
    
    for config_name, stats in results.items():
        config = stats['config']
        rt_ready = "✅" if stats['avg_fps'] > 30 else "❌"
        print(f"{config_name:<10} {str(config['anti_halo']):<10} {config['vibrancy_boost']:<9} "
              f"{config['edge_filter']:<10} {stats['avg_fps']:<8.1f} {rt_ready:<8}")
    
    # Save results
    with open('realtime_benchmark_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to realtime_benchmark_results.json")
    return results

def validate_ssim_targets():
    """Validate SSIM targets across different image types."""
    print("📊 Validating SSIM targets...")
    
    # Create test images with different characteristics
    test_images = []
    
    # High contrast image
    img1 = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.rectangle(img1, (100, 100), (300, 300), (255, 255, 255), -1)
    cv2.rectangle(img1, (350, 150), (550, 350), (0, 0, 0), -1)
    test_images.append(('high_contrast', img1))
    
    # Gradient image
    img2 = np.zeros((480, 640, 3), dtype=np.uint8)
    for i in range(480):
        for j in range(640):
            img2[i, j] = [int(255 * j / 640), int(255 * i / 480), 128]
    test_images.append(('gradient', img2))
    
    # Textured image
    img3 = np.random.randint(100, 200, (480, 640, 3), dtype=np.uint8)
    for i in range(0, 480, 20):
        cv2.line(img3, (0, i), (640, i), (255, 255, 255), 1)
    for j in range(0, 640, 20):
        cv2.line(img3, (j, 0), (j, 480), (255, 255, 255), 1)
    test_images.append(('textured', img3))
    
    enhancer = AdvancedImageEnhancer()
    results = []
    
    for img_type, img in test_images:
        print(f"\n🧪 Testing {img_type} image...")
        
        # Save test image
        test_path = f"test_{img_type}.jpg"
        cv2.imwrite(test_path, img)
        
        # Enhance with different SSIM targets
        ssim_targets = [0.85, 0.90, 0.95]
        
        for target in ssim_targets:
            output_path = f"test_{img_type}_ssim_{target:.2f}.jpg"
            
            success = enhancer.enhance_image(
                input_path=test_path,
                output_path=output_path,
                anti_halo=True,
                vibrancy_boost=1.2,
                edge_filter='bilateral',
                real_time=False,
                ssim_target=target,
                use_ai_model=True
            )
            
            if success:
                achieved_ssim = enhancer.processing_metrics.get('ssim', 0.0)
                target_met = achieved_ssim >= target
                
                results.append({
                    'image_type': img_type,
                    'target_ssim': target,
                    'achieved_ssim': achieved_ssim,
                    'target_met': target_met,
                    'processing_time_ms': enhancer.processing_metrics.get('processing_time_ms', 0)
                })
                
                print(f"   Target: {target:.2f}, Achieved: {achieved_ssim:.3f} {'✅' if target_met else '❌'}")
        
        # Cleanup
        Path(test_path).unlink(missing_ok=True)
        for target in ssim_targets:
            Path(f"test_{img_type}_ssim_{target:.2f}.jpg").unlink(missing_ok=True)
    
    # Summary
    total_tests = len(results)
    successful_targets = sum(1 for r in results if r['target_met'])
    
    print(f"\n📊 SSIM Validation Summary:")
    print(f"   Total tests: {total_tests}")
    print(f"   Targets met: {successful_targets}/{total_tests} ({successful_targets/total_tests*100:.1f}%)")
    print(f"   Average achieved SSIM: {np.mean([r['achieved_ssim'] for r in results]):.3f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Test Advanced Image Enhancement System")
    parser.add_argument("--test-single", help="Test single image")
    parser.add_argument("--test-batch", help="Test batch of images in directory")
    parser.add_argument("--benchmark-realtime", action="store_true",
                       help="Benchmark real-time performance")
    parser.add_argument("--validate-ssim", action="store_true",
                       help="Validate SSIM targets")
    parser.add_argument("--all", action="store_true",
                       help="Run all tests")
    
    args = parser.parse_args()
    
    if not any([args.test_single, args.test_batch, args.benchmark_realtime, 
                args.validate_ssim, args.all]):
        parser.print_help()
        return
    
    print("🎯 Advanced Image Enhancement Test Suite")
    print("=" * 60)
    print("Testing anti-halo sharpening, vibrancy boost, edge-aware filtering")
    print("Real-time optimization, SSIM > 90% validation")
    print("=" * 60)
    
    if args.test_single:
        test_single_image(args.test_single)
    
    if args.test_batch:
        test_batch_images(args.test_batch)
    
    if args.benchmark_realtime or args.all:
        benchmark_realtime_performance()
    
    if args.validate_ssim or args.all:
        validate_ssim_targets()
    
    print("\n🎉 Advanced enhancement testing completed!")
    print("Check the output directories and JSON files for detailed results.")

if __name__ == "__main__":
    main()
