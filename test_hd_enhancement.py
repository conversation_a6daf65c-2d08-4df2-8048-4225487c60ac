#!/usr/bin/env python3
"""
Test HD Enhancement System

Test the true HD quality enhancement for both single images and webcam.
Validates genuine 1080p quality without artistic effects.

Usage:
    python test_hd_enhancement.py --test-image photo.jpg
    python test_hd_enhancement.py --test-webcam
    python test_hd_enhancement.py --benchmark
    python test_hd_enhancement.py --create-samples
"""

import sys
import argparse
from pathlib import Path
import cv2
import numpy as np
import time
import json

def create_test_samples():
    """Create test images for HD enhancement validation."""
    print("🎨 Creating HD test samples...")
    
    samples_dir = Path("hd_test_samples")
    samples_dir.mkdir(exist_ok=True)
    
    # Sample 1: Portrait with fine details
    print("   Creating portrait sample...")
    portrait = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Background gradient
    for i in range(480):
        for j in range(640):
            portrait[i, j] = [200 - i//3, 220 - j//4, 240 - i//4]
    
    # Face oval
    center_x, center_y = 320, 200
    cv2.ellipse(portrait, (center_x, center_y), (80, 100), 0, 0, 360, (220, 180, 160), -1)
    
    # Eyes
    cv2.circle(portrait, (300, 180), 8, (50, 50, 50), -1)
    cv2.circle(portrait, (340, 180), 8, (50, 50, 50), -1)
    cv2.circle(portrait, (302, 178), 3, (255, 255, 255), -1)
    cv2.circle(portrait, (342, 178), 3, (255, 255, 255), -1)
    
    # Nose and mouth
    cv2.ellipse(portrait, (320, 200), (3, 8), 0, 0, 360, (180, 140, 120), -1)
    cv2.ellipse(portrait, (320, 220), (15, 5), 0, 0, 360, (180, 120, 100), -1)
    
    # Hair texture
    for i in range(100, 300):
        for j in range(250, 390):
            if np.random.random() > 0.7:
                portrait[i, j] = [80, 60, 40]
    
    cv2.imwrite(str(samples_dir / "portrait_sample.jpg"), portrait)
    
    # Sample 2: Landscape with fine details
    print("   Creating landscape sample...")
    landscape = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Sky gradient
    for i in range(200):
        landscape[i, :] = [255 - i, 240 - i//2, 200 - i//3]
    
    # Mountains
    points = np.array([[0, 200], [150, 120], [300, 180], [450, 100], [640, 160], [640, 200], [0, 200]], np.int32)
    cv2.fillPoly(landscape, [points], (100, 120, 80))
    
    # Trees (fine details)
    for x in range(50, 600, 40):
        # Tree trunk
        cv2.rectangle(landscape, (x-2, 200), (x+2, 250), (80, 60, 40), -1)
        # Tree crown
        cv2.circle(landscape, (x, 190), 15, (60, 120, 40), -1)
        # Fine branches
        for angle in range(0, 360, 45):
            end_x = int(x + 10 * np.cos(np.radians(angle)))
            end_y = int(190 + 10 * np.sin(np.radians(angle)))
            cv2.line(landscape, (x, 190), (end_x, end_y), (40, 80, 20), 1)
    
    # Ground texture
    for i in range(250, 480):
        for j in range(640):
            if np.random.random() > 0.8:
                landscape[i, j] = [40 + np.random.randint(0, 20), 80 + np.random.randint(0, 20), 20 + np.random.randint(0, 10)]
            else:
                landscape[i, j] = [60, 100, 40]
    
    cv2.imwrite(str(samples_dir / "landscape_sample.jpg"), landscape)
    
    # Sample 3: Text and fine details
    print("   Creating text detail sample...")
    text_img = np.ones((480, 640, 3), dtype=np.uint8) * 240
    
    # Add various text sizes
    font = cv2.FONT_HERSHEY_SIMPLEX
    texts = [
        ("HD QUALITY TEST", 1.5, (50, 80)),
        ("Fine Detail Enhancement", 1.0, (50, 150)),
        ("1080p Resolution Target", 0.8, (50, 200)),
        ("Sharpness and Clarity", 0.6, (50, 240)),
        ("Micro-detail preservation", 0.5, (50, 280)),
        ("Edge enhancement validation", 0.4, (50, 320))
    ]
    
    for text, scale, pos in texts:
        cv2.putText(text_img, text, pos, font, scale, (0, 0, 0), 2)
        cv2.putText(text_img, text, (pos[0]+1, pos[1]+1), font, scale, (100, 100, 100), 1)
    
    # Add fine grid pattern
    for i in range(0, 480, 20):
        cv2.line(text_img, (0, i), (640, i), (200, 200, 200), 1)
    for j in range(0, 640, 20):
        cv2.line(text_img, (j, 0), (j, 480), (200, 200, 200), 1)
    
    cv2.imwrite(str(samples_dir / "text_detail_sample.jpg"), text_img)
    
    print(f"✅ Test samples created in {samples_dir}")
    return samples_dir

def test_single_image(image_path):
    """Test HD enhancement on a single image."""
    print(f"🔍 Testing HD enhancement on: {image_path}")
    
    from hd_enhance import HDEnhancer
    
    enhancer = HDEnhancer()
    
    # Load original
    original = cv2.imread(image_path)
    if original is None:
        print(f"❌ Could not load image: {image_path}")
        return False
    
    original_shape = original.shape
    print(f"📏 Original: {original_shape[1]}x{original_shape[0]}")
    
    # Enhance
    start_time = time.time()
    enhanced, proc_time = enhancer.enhance_image(original)
    total_time = time.time() - start_time
    
    enhanced_shape = enhanced.shape
    print(f"📐 Enhanced: {enhanced_shape[1]}x{enhanced_shape[0]}")
    print(f"⏱️  Processing time: {proc_time*1000:.1f}ms")
    print(f"⏱️  Total time: {total_time*1000:.1f}ms")
    
    # Save result
    output_path = f"{Path(image_path).stem}_hd_test.jpg"
    cv2.imwrite(output_path, enhanced, [cv2.IMWRITE_JPEG_QUALITY, 100])
    print(f"✅ HD enhanced saved: {output_path}")
    
    # Create comparison
    comparison_path = create_comparison(original, enhanced, f"{Path(image_path).stem}_hd_comparison.jpg")
    if comparison_path:
        print(f"📊 Comparison saved: {comparison_path}")
    
    # Quality metrics
    scale_factor = enhanced_shape[1] / original_shape[1]
    is_hd = enhanced_shape[0] >= 1080 or enhanced_shape[1] >= 1920
    fps_equivalent = 1000 / (proc_time * 1000) if proc_time > 0 else 0
    
    print(f"📈 Results:")
    print(f"   Scale factor: {scale_factor:.1f}x")
    print(f"   HD quality: {'✅' if is_hd else '❌'}")
    print(f"   FPS equivalent: {fps_equivalent:.1f}")
    
    return True

def create_comparison(original, enhanced, output_path):
    """Create side-by-side comparison."""
    try:
        # Resize original to match enhanced height for comparison
        h_enhanced = enhanced.shape[0]
        h_original = original.shape[0]
        w_original = original.shape[1]
        
        if h_enhanced != h_original:
            scale = h_enhanced / h_original
            new_w = int(w_original * scale)
            original_resized = cv2.resize(original, (new_w, h_enhanced), interpolation=cv2.INTER_LANCZOS4)
        else:
            original_resized = original
        
        # Create comparison
        comparison = np.hstack([original_resized, enhanced])
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (0, 255, 0), 2)
        cv2.putText(comparison, "HD Enhanced", (original_resized.shape[1] + 10, 30), font, 1, (0, 255, 0), 2)
        
        cv2.imwrite(output_path, comparison, [cv2.IMWRITE_JPEG_QUALITY, 100])
        return output_path
        
    except Exception as e:
        print(f"❌ Error creating comparison: {e}")
        return None

def test_webcam():
    """Test HD webcam enhancement."""
    print("🎥 Testing HD webcam enhancement...")
    print("This will start the webcam. Press 'q' to quit.")
    
    try:
        from hd_enhance import HDWebcamEnhancer
        
        webcam_enhancer = HDWebcamEnhancer()
        webcam_enhancer.run_webcam()
        
    except Exception as e:
        print(f"❌ Webcam test failed: {e}")
        return False
    
    return True

def benchmark_hd_enhancement():
    """Benchmark HD enhancement performance."""
    print("🏁 Benchmarking HD enhancement performance...")
    
    from hd_enhance import HDEnhancer
    
    # Create test images of different sizes
    test_sizes = [
        (320, 240),   # QVGA
        (640, 480),   # VGA
        (1280, 720),  # HD
        (1920, 1080)  # Full HD
    ]
    
    enhancer = HDEnhancer()
    results = {}
    
    for width, height in test_sizes:
        size_name = f"{width}x{height}"
        print(f"\n🧪 Testing {size_name}...")
        
        # Create test image
        test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # Add some structure
        cv2.rectangle(test_image, (width//4, height//4), (3*width//4, 3*height//4), (255, 255, 255), -1)
        cv2.circle(test_image, (width//2, height//2), min(width, height)//6, (0, 0, 255), -1)
        
        # Benchmark multiple runs
        times = []
        for i in range(5):
            enhanced, proc_time = enhancer.enhance_image(test_image)
            times.append(proc_time * 1000)  # Convert to ms
        
        # Calculate statistics
        avg_time = np.mean(times)
        min_time = np.min(times)
        max_time = np.max(times)
        fps = 1000 / avg_time if avg_time > 0 else 0
        
        enhanced_shape = enhanced.shape
        scale_factor = enhanced_shape[1] / width
        
        results[size_name] = {
            'input_size': (width, height),
            'output_size': (enhanced_shape[1], enhanced_shape[0]),
            'avg_time_ms': avg_time,
            'min_time_ms': min_time,
            'max_time_ms': max_time,
            'fps': fps,
            'scale_factor': scale_factor,
            'is_hd_output': enhanced_shape[0] >= 1080 or enhanced_shape[1] >= 1920
        }
        
        print(f"   Average: {avg_time:.1f}ms ({fps:.1f} FPS)")
        print(f"   Output: {enhanced_shape[1]}x{enhanced_shape[0]} ({scale_factor:.1f}x scale)")
        print(f"   HD quality: {'✅' if results[size_name]['is_hd_output'] else '❌'}")
    
    # Print summary table
    print(f"\n📊 HD Enhancement Benchmark Results:")
    print("=" * 80)
    print(f"{'Input Size':<12} {'Output Size':<12} {'Avg Time':<10} {'FPS':<8} {'Scale':<8} {'HD':<4}")
    print("-" * 80)
    
    for size_name, stats in results.items():
        input_size = f"{stats['input_size'][0]}x{stats['input_size'][1]}"
        output_size = f"{stats['output_size'][0]}x{stats['output_size'][1]}"
        hd_status = "✅" if stats['is_hd_output'] else "❌"
        
        print(f"{input_size:<12} {output_size:<12} {stats['avg_time_ms']:<10.1f} "
              f"{stats['fps']:<8.1f} {stats['scale_factor']:<8.1f} {hd_status:<4}")
    
    # Save results
    with open('hd_benchmark_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to hd_benchmark_results.json")
    
    # Performance summary
    hd_capable = sum(1 for r in results.values() if r['is_hd_output'])
    realtime_capable = sum(1 for r in results.values() if r['fps'] >= 15)
    
    print(f"\n📈 Performance Summary:")
    print(f"   HD output capable: {hd_capable}/{len(results)} test cases")
    print(f"   Real-time capable (≥15 FPS): {realtime_capable}/{len(results)} test cases")
    print(f"   Best FPS: {max(r['fps'] for r in results.values()):.1f}")
    print(f"   Average processing time: {np.mean([r['avg_time_ms'] for r in results.values()]):.1f}ms")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Test HD Enhancement System")
    parser.add_argument("--test-image", help="Test single image enhancement")
    parser.add_argument("--test-webcam", action="store_true", help="Test webcam enhancement")
    parser.add_argument("--benchmark", action="store_true", help="Run performance benchmark")
    parser.add_argument("--create-samples", action="store_true", help="Create test samples")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    if not any([args.test_image, args.test_webcam, args.benchmark, args.create_samples, args.all]):
        parser.print_help()
        return
    
    print("🎯 HD Enhancement Test Suite")
    print("=" * 50)
    print("Testing true 1080p HD quality enhancement")
    print("=" * 50)
    
    if args.create_samples or args.all:
        samples_dir = create_test_samples()
        if args.all:
            # Test with created samples
            for sample in samples_dir.glob("*.jpg"):
                print(f"\n{'='*50}")
                test_single_image(str(sample))
    
    if args.test_image:
        test_single_image(args.test_image)
    
    if args.benchmark or args.all:
        benchmark_hd_enhancement()
    
    if args.test_webcam or args.all:
        print(f"\n{'='*50}")
        test_webcam()
    
    print("\n🎉 HD enhancement testing completed!")

if __name__ == "__main__":
    main()
